<div *ngIf="isLoading">
    <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
</div>
<p-toast></p-toast>
<p-confirmDialog [style]="{width: '395px'}"></p-confirmDialog>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<div class="container-assignments">
    <div *ngIf="listAssignments.length != 0 else empty" class="content-list-assignments gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <label class="title-list-assignments">{{ "titles.assignment" | translate}}</label>
            <div class="flex flex-row flex-wrap justify-content-center gap-4 align-items-center">
                <p-iconField iconPosition="right">
                    <input pInputText type="text"
                        [(ngModel)]="searchValue"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="{{ 'content.search' | translate }}"
                    />
                    <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                </p-iconField>
                <div class="add-action-main-full">
                    <p-button
                        [disabled]="!readAndWritePermissions"
                        [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'assignment.new_assignment'| translate }}" icon="pi pi-plus" iconPos="right" [rounded]="true"
                        (onClick)="createNewAssignment()"></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button
                        [disabled]="!readAndWritePermissions"
                        [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        icon="pi pi-plus" [rounded]="true"
                        (onClick)="createNewAssignment()"></p-button>
                </div>
            </div>
        </div>
        <div>

        </div>
        <p-table
            #dt
            [value]="listAssignments"
            (onFilter)="onFilter($event, dt)"
            dataKey="id"
            [rowHover]="true"
            [paginator]="true"
            [rows]="10"
            [rowsPerPageOptions]="[5, 10, 20]"
            [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="['name', 'updatedAt']"
            [sortField]="'name'" [sortOrder]="1">
            <ng-template pTemplate="header">
                <tr>
                    <th class="fixed-column-sm" pSortableColumn="name">{{'assignment.name' | translate}}<p-sortIcon field="name"></p-sortIcon></th>
                    <th class="fixed-column-sm">{{ 'assignment.num_flows' | translate }}</th>
                    <th class="fixed-column-sm">{{ 'assignment.num_categories' | translate }}</th>
                    <th class="fixed-column-sm" pSortableColumn="updatedAt">{{ 'assignment.updated_at' | translate }}<p-sortIcon field="updatedAt"></p-sortIcon></th>
                    <th pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th>
                        <p-columnFilter type="text" field="name" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th></th>
                    <th></th>
                    <th>
                        <p-columnFilter type="date" field="updatedAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroup">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'updatedAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr></tr>
            </ng-template>
            <ng-template pTemplate="body" let-data>
                <tr [pSelectableRow]="data">
                    <td (click)="editAssignment(data)" showDelay="1000" pTooltip="{{data.name}}" tooltipPosition="top" class="ellipsis-cell">{{data.name}}</td>
                    <td (click)="editAssignment(data)" showDelay="1000" pTooltip="{{getNumElements(data.elements, 'FLOW')}}" tooltipPosition="top" class="ellipsis-cell">{{getNumElements(data.elements, 'FLOW')}}</td>
                    <td (click)="editAssignment(data)" showDelay="1000" pTooltip="{{getNumElements(data.elements, 'CATEGORY')}}" tooltipPosition="top" class="ellipsis-cell">{{getNumElements(data.elements, 'CATEGORY')}}</td>
                    <td (click)="editAssignment(data)" showDelay="1000" pTooltip="{{data.updatedAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{data.updatedAt | date:('dateTimeFormat' | translate)}}</td>
                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border" style="width: 10%;">
                        <div class="flex flex-row">
                            <button pButton pRipple icon="pi pi-eye" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="showAssignmentDetails(data)"></button>
                            <button pButton pRipple icon="pi pi-pencil" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="editAssignment(data)"></button>
                            <button pButton pRipple icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="deleteAssignment(data)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>

<p-dialog [(visible)]="showNewAssignmentDialog" styleClass="p-fluid" [closable]="true" (onHide)="resetStates()" [modal]="true">
    <ng-template pTemplate="header">
        <div></div>
        <div class="dialog-title">
            {{ dialogBoxTitle | translate }}
        </div>
    </ng-template>
    <ng-template pTemplate="content">
        @if(isNewAssignment==true){
            <p-steps
            [model]="items"
            [readonly]="false"
            [activeIndex]="activeIndex"/>
        }@else {
            <div class="flex flex-col justify-content-center">
                <button
                    id="first"
                    [style]="{'border-radius': '6px 0px 0px 6px'}"
                    class="active"
                    (click)="onFirstTabClick()">
                    {{ 'assignment.general' | translate }}
                </button>
                <button id="second" class="general"
                    [style]="{'border-radius': '0px 0px 0px 0px'}"
                    (click)="onSecondTabClick()">
                    {{ 'assignment.flows' | translate }}
                </button>
                <button id="third"
                    [style]="{'border-radius': '0px 0px 0px 0px'}"
                    class="general"
                    (click)="onThirdTabClick()">
                    {{ 'assignment.categories' | translate }}
                </button>
                <button id="fourth"
                    [style]="{'border-radius': '0px 6px 6px 0px'}"
                    class="general"
                    (click)="onFourthTabClick()">
                    {{ 'assignment.configuration' | translate }}
                </button>
            </div>
        }
        @switch (activeIndex) {
            @case (0){
                <app-group-info (dataValid)="assignmentInfoDataValid($event)" (outputData)="getGroupData($event)" [inputData]="assignmentInfoData"></app-group-info>
            }
            @case (1) {
                <app-flow-assignment (listFlows)="getSelectedFlows($event)" [inputData]="selectedFlows"></app-flow-assignment>
            }
            @case(2) {
                <app-category-assignment [canReadAndWrite]="readAndWritePermissions" (listCategories)="getSelectedCategories($event)" [inputData]="selectedCategories"></app-category-assignment>
            }
            @case (3) {
                <p-scrollPanel [style]="{ maxWidth: '43vw', height: '55vh' }">
                    <div class="m-2">
                        <app-group-config
                            [canReadAndWrite]="readAndWritePermissions"
                            [inputData]="assignmentInfoData"
                            [selectedConfigSchedules]="selectedConfigSchedules"
                            [selectedConfigUsers]="selectedConfigUsers"
                            (dataValid)="assignmentInfoDataValid($event)"
                            (outputData)="getGroupConfigData($event)"
                        ></app-group-config>
                    </div>
                </p-scrollPanel>
            }
        }
        <div class="flex align-content-center justify-content-center mt-3 gap-3">
            @if (activeIndex == 0) {
                <p-button
                    label="{{ 'cancel' | translate }}"
                    (onClick)="onCancel()"
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
                >
                </p-button>
            }
            @else {
                <p-button
                    label="{{ (isNewAssignment? 'back' : 'cancel') | translate }}"
                    icon="{{ isNewAssignment ? 'pi pi-angle-left' : '' }}"
                    (onClick)="isNewAssignment? onBack(): onCancel()"
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
                >
                </p-button>
            }
            @if (activeIndex == items.length - 1) {
                <p-button
                    label="{{ 'save' | translate }}"
                    iconPos="right"
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
                    [disabled]="disableContinue? true : false"
                    (onClick)="isNewAssignment? onSave(): onUpdate()"
                >
                </p-button>
            }
            @else {
                <p-button
                    label="{{ secondaryButtonLabel | translate}}"
                    iconPos="right"
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
                    [disabled]="activeIndex == 0 && disableContinue? true : false"
                    (onClick)="isNewAssignment? onNext(): onUpdate()"
                >
                </p-button>
            }
        </div>
        <div>
        </div>
    </ng-template>
</p-dialog>

<p-dialog [(visible)]="showFlowCategoryDialog" [style]="{ width: '392px' }" header="{{ 'messages.warning' | translate }}" [modal]="true" styleClass="p-fluid">
    <ng-template pTemplate="content">
        <div class="flex flex-row align-content-center justify-content-start mt-3 gap-3">
            <i class="pi pi-exclamation-triangle" style="font-size: 28px"></i>
            {{ flowCategoryMessage | translate }}
        </div>
    </ng-template>
     <ng-template pTemplate="footer">
        <div class="flex flex-col justify-content-center">
            @if(showCreateFlowButton){
                <p-button label="{{ 'flow.flow' | translate }}" class="p-button-text" ic (click)="onAddFlow()" icon="pi pi-plus" iconPos="right"
                    [style]="{'color': '#FFFFFF', 'width': '130px', 'background': '#204887' }"
                ></p-button>
            }
            @if(showCreateCategoryButton){
                <p-button label="{{ 'category.category' | translate }}" class="p-button-text" ic (click)="onAddCategory()" icon="pi pi-plus" iconPos="right"
                    [style]="{'color': '#FFFFFF', 'width': '130px', 'background': '#204887' }"
                ></p-button>
            }
        </div>
     </ng-template>
</p-dialog>

<p-dialog [(visible)]="showAssignmentDetailsDialog" styleClass="p-fluid" [closable]="true" [header]="assignmentName" [modal]="true" styleClass="p-fluid">
    <ng-template pTemplate="content">
        <div class="flex flex-row align-content-center justify-content-start mt-3 gap-3">
            <app-detail-assignment [assignmentData]="selectedAssignment" [listSelectedFlows]="selectedObjectFlows" [listSelectedCategories]="selectedObjectCategories"></app-detail-assignment>
        </div>
    </ng-template>
    <ng-template pTemplate="footer">
        <div class="flex flex-col justify-content-center">
            <p-button label="{{ 'config.close' | translate }}"
                (onClick)="closeAssignmentDetails()"
                    [style]="{ 'width':'122px', 'height':'36px', 'border':'2px solid #64748B', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
                </p-button>
        </div>
    </ng-template>
</p-dialog>

<ng-template #empty>
    <div class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ 'titles.assignment' | translate}}</label>
                </div>
            </div>
        </div>
    </div>
    <div style="height: 65vh;" class="flex justify-content-center align-items-center">
        <app-empty
            [readAndWritePermissions]="readAndWritePermissions"
            buttonLabel="assignment.new_assignment"
            titleLabel="assignment.no_assignment_available"
            contentHeight="300px"
            (clicked)="createNewAssignment()"
        ></app-empty>
    </div>
</ng-template>