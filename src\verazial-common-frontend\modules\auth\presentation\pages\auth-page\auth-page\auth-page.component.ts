import {DOCUMENT} from '@angular/common';
import {Component, Inject, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {TranslateService} from '@ngx-translate/core';
import {Message, MessageService} from 'primeng/api';
import {WidgetResult} from 'src/verazial-common-frontend/modules/shared/models/widget-response.model';
import {
  AuthByLicenseRequestEntity
} from 'src/verazial-common-frontend/core/general/auth/domain/entity/auth-by-license-request.entity';
import {
  AuthenticateByLicenseUseCase
} from 'src/verazial-common-frontend/core/general/auth/domain/use-cases/authenticate-by-license.use-case';
import {
  RefreshByUserUseCase
} from 'src/verazial-common-frontend/core/general/auth/domain/use-cases/refresh-by-user.use-case';
import {RoleEntity} from 'src/verazial-common-frontend/core/general/common/entity/role.entity';
import {
  KonektorPropertiesEntity
} from 'src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity';
import {
  GetKonektorPropertiesUseCase
} from 'src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case';
import {GeneralSettings} from 'src/verazial-common-frontend/core/general/manager/common/models/general-settings.model';
import {
  GetSettingsByApplicationUseCase
} from 'src/verazial-common-frontend/core/general/manager/domain/use-cases/get-settings-by-application.use-case';
import {
  GetAccessByRoleIdUseCase
} from 'src/verazial-common-frontend/core/general/role/domain/use-cases/roles-access/get-access-by-role-id.use-case';
import {UserEntity} from 'src/verazial-common-frontend/core/general/user/domain/entity/user.entity';
import {
  GetUserByNumIdUseCase
} from 'src/verazial-common-frontend/core/general/user/domain/use-cases/get-user-by-num-id.use-case';
import {AuditTrailActions} from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import {AuditTrailFields} from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import {ErrorResponse} from 'src/verazial-common-frontend/core/models/error-response';
import {AuditTrailService} from 'src/verazial-common-frontend/core/services/audit-trail.service';
import {LocalStorageService} from 'src/verazial-common-frontend/core/services/local-storage.service';
import {environment} from 'src/environments/environment';
import {
  BiometricLoginUseCase
} from 'src/verazial-common-frontend/core/general/user/domain/use-cases/biometric-login.use-case';
import {ConsoleLoggerService} from 'src/verazial-common-frontend/core/services/console-logger.service';
import {CheckPermissionsService} from 'src/verazial-common-frontend/core/services/check-permissions-service';
import {AccessIdentifier} from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import {
  GetRawStaticResourceByIdUseCase
} from 'src/verazial-common-frontend/core/general/storage/domain/use-cases/get-raw-static-resource-by-id.use-case';
import {getAuthError} from 'src/verazial-common-frontend/core/util/supporting-functions';

@Component({
  selector: 'app-auth-page',
  templateUrl: './auth-page.component.html',
  styleUrl: './auth-page.component.css',
  providers: [MessageService]
})
export class AuthPageComponent implements OnInit, OnDestroy {

  initialized: boolean = true;
  alertMessage!: Message[];
  isLoading: boolean = false;
  showRoleSelection: boolean = false;
  user: UserEntity | undefined;
  userRoles: RoleEntity[] | undefined;
  userId: string | undefined;
  technology!: string;
  showWidgetDialog: boolean = false;
  showWidgetSearchDialog: boolean = false;
  showUpdatePassword: boolean = false;
  showResetPassword: boolean = false;
  showPasswordRecovery: boolean = false;
  token: string = "";
  recoveryToken: string = "";
  application: string = environment.application;
  general_settings: string = "GENERAL_SETTINGS"
  customLoginLogo: boolean = false;
  customLoginLogoSource: string = `verazial-common-frontend/assets/images/${ this.application.toLowerCase() }/appLogo.svg`;
  validatorPattern: string | RegExp = /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/;

  noLicenseConfigured: boolean = false;

  // Biometric login
  userNumId: string = "";
  widgetUrl: string = "";
  managerSettings: GeneralSettings | undefined;
  generalSettings: GeneralSettings = {};
  konektorProperties?: KonektorPropertiesEntity;
  segmentedSearchAttributes: any[] = [{
    name: 'Is User',
    value: 'true',
    secondSearch: 'false',
  }]

  constructor(
    @Inject(DOCUMENT) private _document: Document,
    private messageService: MessageService,
    private translateService: TranslateService,
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService,
    private router: Router,
    private route: ActivatedRoute,
    private getAccessByRoleIdUseCase: GetAccessByRoleIdUseCase,
    private getUserByNumIdUseCase: GetUserByNumIdUseCase,
    private refreshByUserUseCase: RefreshByUserUseCase,
    private auditTrailService: AuditTrailService,
    private authenticateByLicenseUseCase: AuthenticateByLicenseUseCase,
    private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
    private getSettingsByApplicationUseCase: GetSettingsByApplicationUseCase,
    private biometricLoginUseCase: BiometricLoginUseCase,
    private checkPermissionsService: CheckPermissionsService,
    private getRawStaticResourceByIdUseCase: GetRawStaticResourceByIdUseCase,
  ) { }

  ngOnInit(): void {
    this._document.body.style.background = '#009BA9';
    this.localStorageService.setLockMenu(false);
    this.getKonektorProperties();
  }

  ngOnDestroy(): void {
    this.localStorageService.setSessionSettings({});
  }

  getKonektorProperties() {
    this.isLoading = true;
    this.getKonektorPropertiesUseCase.execute().subscribe({
      next: (data) => {
        if (data) {
          this.konektorProperties = data;
          if (data.apiGatewayGrpc) {
            this.localStorageService.setApiGatewayURL(data.apiGatewayGrpc);
          }
          else {
            this.localStorageService.destroyApiGatewayURL();
          }
          if (!this.konektorProperties.license) {
            this.loggerService.error("Konektor S")
            this.loggerService.error("No License")
            this.isLoading = false;
            this.noLicenseConfigured = true;
            return;
          }
          this.noLicenseConfigured = false;
          let authByLicenseRequest: AuthByLicenseRequestEntity = {
            license: this.konektorProperties.license,
            deviceHash: this.konektorProperties.deviceHash,
            applicationName: this.application
          }
          this.authenticateByLicenseUseCase.execute(authByLicenseRequest).then(
            (data) => {
              if (data) {
                this.token = data.token!;
                this.getSettingsByApplicationUseCase.execute({ applicationName: this.application, token: this.token }).then(
                  (data) => {
                    if (data.settings) {
                      this.managerSettings = data.settings;
                      this.localStorageService.setSessionSettings(data.settings);
                      this.getCustomLoginLogo();
                    }
                    else {
                      this.loggerService.error("System Settings MS " + this.application)
                      this.loggerService.error("No Settings")
                    }
                    this.isLoading = false;
                  },
                  (e) => {
                    this.loggerService.error("System Settings MS " + this.application)
                    this.loggerService.error(e);
                    this.isLoading = false;
                  },
                )
                .finally(() => {
                  this.getSettingsByApplicationUseCase.execute({ applicationName: this.general_settings, token: this.token }).then(
                    (data) => {
                      if (data.settings) {
                        this.generalSettings = data.settings;
                        this.validatorPattern = this.generalSettings?.continued1?.passwordComplexity?.regex != "" ? this.generalSettings?.continued1?.passwordComplexity?.regex ?? this.validatorPattern : this.validatorPattern;
                      }
                      else {
                        this.loggerService.error("System Settings MS " + this.general_settings)
                        this.loggerService.error("No Settings")
                      }
                      this.isLoading = false;
                    },
                    (e) => {
                      this.loggerService.error("System Settings MS " + this.general_settings)
                      this.loggerService.error(e);
                      this.isLoading = false;
                    },
                  );
                });
              }
              else {
                this.loggerService.error("Auth MS")
                this.loggerService.error("No Token")
                this.isLoading = false;
              }
            },
            (e) => {
              this.loggerService.error("Auth MS")
              this.loggerService.error(e);
              this.isLoading = false;
            }
          )
          .finally(() => {
            this.route.params.subscribe(params => {
              this.recoveryToken = params['recoveryToken'];
              if(this.recoveryToken != "" && this.recoveryToken != undefined && this.recoveryToken != null) {
                this.showPasswordRecovery = true;
              }
            });
          });
        } else {
          this.loggerService.error("Konektor S")
          let message = this.translateService.instant("messages.konektor_properties_not_found")
          this.loggerService.error(message);
          this.isLoading = false;
        }
      },
      error: (e) => {
        this.loggerService.error("Konektor S")
        let message = this.translateService.instant("messages.konektor_connection_error")
        this.loggerService.error(message);
        this.isLoading = false;
      }
    });
  }

  receiveSubject(data: UserEntity) {
    this.isLoading = true;
    if (data.mustUpdatePassword) {
      this.user = data;
      this.showUpdatePassword = true;
      this.isLoading = false;
      return
    }
    this.localStorageService.saveUser(data);
    if (data.roles && data.roles.length > 1) {
      if (data.defaultRole == "") {
        this.userId = data.id;
        this.userRoles = data.roles as RoleEntity[];
        this.showRoleSelection = true;
        this.isLoading = false;
      } else {
        this.onAccept(data.defaultRole as string);
      }
    } else if (data.roles && data.roles.length == 1) {
      this.onAccept(data.roles[0]);
    } else {
      this.messageService.add(
        {
          severity: 'error',
          summary: this.translateService.instant("titles.unauthorized"),
          detail: `${this.translateService.instant("messages.unauthorized")}`
        },
      );
    }
  }

  onAccept(role: RoleEntity | string) {
    let selectedRole!: RoleEntity;
    if (typeof role === "string") {
      const user = this.localStorageService.getUser()
      let userRoles = user.roles as RoleEntity[];
      let roleFilter = userRoles.find((v, k) => v.id == Number(role));
      if (roleFilter) {
        selectedRole = roleFilter;
      }
      else {
        if (user.roles.length > 1) {
          this.userId = user.id;
          this.userRoles = user.roles as RoleEntity[];
          this.showRoleSelection = true;
          this.isLoading = false;
          return;
        }
      }
    } else {
      selectedRole = role;
    }
    this.getRoleAccesses(selectedRole)
    this.showRoleSelection = false;
  }

  onCancel() {
    this.showRoleSelection = false;
  }

  showErrorMessage(error: ErrorResponse) {
    this.messageService.add(
      {
        severity: 'error',
        summary: error.title,
        detail: `${this.translateService.instant(error.code.toString())} - ${this.translateService.instant(error.message != "" ? error.message : "messages.error_general")}`
      },
    );
  }

  authError(event: { errorCode: string, numId: string }, biometric = false) {
    let userData: UserEntity | undefined = undefined;
    this.getUserByNumIdUseCase.execute({ numId: event.numId, authToken: this.token }).then(
      (data) => {
        userData = data;
      },
      (e) => {
        this.loggerService.error("Users MS")
        this.loggerService.error(e)
        let error: ErrorResponse = {
          title: this.translateService.instant("content.errorTitle"),
          code: "0",
          message: this.translateService.instant("messages.error_login")
        }
        this.showErrorMessage(error);
      }
    )
    .finally(() => {
      if (userData) {
        let errorTitle = '';
        let errorMessage = '';
        if (userData) {
          errorTitle = this.translateService.instant("content.errorTitle");
          switch (event.errorCode) {
            case '':
            case '561':
              console.log("561")
              errorMessage = this.translateService.instant("messages.user_password_error");
              if (biometric){
                if (this.generalSettings?.biometricVerification?.isEnable) {
                  if (userData.loginAttempts! > 1 && userData.loginAttempts! < this.generalSettings.biometricVerification?.loginAttempts! - 1) {
                    errorMessage = errorMessage + this.translateService.instant("messages.user_remaining_attempts_description").replace("{0}", (this.generalSettings.biometricVerification?.loginAttempts! - userData.loginAttempts!).toString());
                  }
                  if (userData.loginAttempts == this.generalSettings.biometricVerification?.loginAttempts! - 1) {
                    errorMessage = errorMessage + this.translateService.instant("messages.user_lock_on_next_attempt");
                  }
                }
              }
              else {
                if (this.generalSettings?.userPasswordVerification?.isEnable) {
                  if (userData.loginAttempts! > 1 && userData.loginAttempts! < this.generalSettings.userPasswordVerification?.loginAttempts! - 1) {
                    errorMessage = errorMessage + this.translateService.instant("messages.user_remaining_attempts_description").replace("{0}", (this.generalSettings.userPasswordVerification?.loginAttempts! - userData.loginAttempts!).toString());
                  }
                  if (userData.loginAttempts == this.generalSettings.userPasswordVerification?.loginAttempts! - 1) {
                    errorMessage = errorMessage + this.translateService.instant("messages.user_lock_on_next_attempt");
                  }
                }
              }
              break;
            case '2002':
              console.log("2002")
              // SessionsExceeded
              errorTitle = this.translateService.instant("ms_errors." + event.errorCode);
              errorMessage = this.translateService.instant("messages.user_sessions_exceeded_description").replace("{0}", this.generalSettings.concurrentSessions?.numberSessions!); 1
              break;
            case '2003':
              console.log("2003")
              // UserBlocked
              errorTitle = this.translateService.instant("ms_errors." + event.errorCode);
              const lockExpires = userData.lockExpires;
              console.log(lockExpires)
              const now = new Date();
              const lockTime = new Date(lockExpires!);
              const diff = Math.abs(lockTime.getTime() - now.getTime());
              const diffMinutes = Math.floor(diff / (1000 * 60));
              const diffSeconds = Math.floor(diff / 1000) % 60;
              const diffHours = Math.floor(diff / (1000 * 60 * 60)) % 24;
              const diffDays = Math.floor(diff / (1000 * 60 * 60 * 24));
              let timeLeft = "";
              if (diffDays > 0) {
                timeLeft += diffDays + " " + this.translateService.instant("content.day") + ", ";
              }
              if (diffHours > 0) {
                timeLeft += diffHours + " " + this.translateService.instant("content.hour") + ", ";
              }
              if (diffMinutes > 0) {
                timeLeft += diffMinutes + " " + this.translateService.instant("content.minute") + ", ";
              }
              if (diffSeconds > 0) {
                timeLeft += diffSeconds + " " + this.translateService.instant("content.second") + ", ";
              }
              errorMessage = this.translateService.instant("messages.user_blocked_description").replace("{0}", timeLeft.replace(/, $/, ""));
              console.log(errorMessage)
              break;
            default:
              console.log("default")
              errorMessage = this.translateService.instant("ms_errors." + event.errorCode)
              break;
          }
        }
        else {
          errorTitle = this.translateService.instant("content.errorTitle");
          errorMessage = this.translateService.instant("messages.user_password_error");
        }
        let error: ErrorResponse = {
          title: errorTitle,
          code: event.errorCode,
          message: errorMessage
        }
        console.log(error)
        this.showErrorMessage(error);
        this.isLoading = false;
      }
      else {
        let error: ErrorResponse = {
          title: this.translateService.instant("content.errorTitle"),
          code: "0",
          message: this.translateService.instant("messages.error_login")
        }
        this.showErrorMessage(error);
      }
      this.isLoading = false;
    });
  }

  /**
   *
   * @param roleId Get all subject's accesses by role ID
   */
  async getRoleAccesses(role: RoleEntity) {
    this.isLoading = true;
    this.getAccessByRoleIdUseCase.execute({ roleId: role.id as number }).then(
      (data) => {
        //let accesses = data.filter((v,k) => v.application == this.application || v.application == 'ALL' );
        let accesses = data;
        this.localStorageService.saveRole(role);
        this.localStorageService.saveAccesses(accesses);
        if (this.noLicenseConfigured) {
          if (this.checkPermissionsService.hasReadAndWritePermissions(AccessIdentifier.LOGIN_WITHOUT_LICENSE)){
            this.router.navigate(['/home']);
          }
          else {
            this.messageService.add(
              {
                severity: 'error',
                summary: this.translateService.instant("titles.unauthorized"),
                detail: `${this.translateService.instant("messages.error_cannot_access_without_license")}`
              },
            );
            this.localStorageService.clean();
            this.getKonektorProperties();
          }
        }
        else {
          this.router.navigate(['/home']);
        }
      },
      (e) => {
        this.loggerService.error("User Role Access MS")
        this.loggerService.error(e);
        this.messageService.add(
          {
            severity: 'error',
            summary: this.translateService.instant("titles.error_role_access"),
            detail: `${this.translateService.instant("messages.error_getting_access")}`
          },
        );
      }
    )
      .finally(() => {
        this.isLoading = false;
      });
  }

  // Getting the result of of updating password
  updatePasswordResult(updateStatus: boolean) {
    if (!updateStatus) {
      this.messageService.add(
        {
          severity: 'error',
          summary: this.translateService.instant("content.errorTitle"),
          detail: `${this.translateService.instant("messages.error_updating_password")}`
        },
      );
    }
    else {
      this.messageService.add(
        {
          severity: 'success',
          summary: this.translateService.instant("content.successTitle"),
          detail: `${this.translateService.instant("messages.password_updated")}`
        },
      );
    }
    this.showUpdatePassword = false;
  }

  widgetVerify(event: { tech: string, numId: string }) {
    if (event.numId == "") {
      this.technology = event.tech;
      this.userNumId = event.numId;
      this.widgetUrl = this.localStorageService.getSessionSettings()?.widgetConfig?.url ?? "";
      if (this.widgetUrl == "") {
        this.messageService.add({
          severity: 'error',
          summary: this.translateService.instant("titles.error"),
          detail: `${this.translateService.instant("messages.error_widget_url")}`
        })
      }
      else {
        this.showWidgetSearchDialog = true;
      }
    }
    else {
      this.technology = event.tech;
      this.userNumId = event.numId;
      this.widgetUrl = this.localStorageService.getSessionSettings()?.widgetConfig?.url ?? "";
      if (this.widgetUrl == "") {
        this.messageService.add({
          severity: 'error',
          summary: this.translateService.instant("titles.error"),
          detail: `${this.translateService.instant("messages.error_widget_url")}`
        })
      }
      else {
        this.showWidgetDialog = true;
      }
    }
  }

  onUserWidgetMatchResult(event: WidgetResult) {
    // this.loggerService.debug("Widget Result", event);
    // this.loggerService.debug(event);
    if (!this.showWidgetDialog && !this.showWidgetSearchDialog) {
      return;
    }
    switch (event.action) {
      case "verify":
        if (event.result == "success") {
          this.biometricLoginUseCase.execute({ numId: this.userNumId, isMatched: event.data.isMatched, authToken: this.token }).then(
            (data) => {
              console.log(data);
              if (data.success && event.data.isMatched && event.data.token) {
                this.localStorageService.setUserVerified(event.data.token);
                this.localStorageService.saveToken(event.data.token);
                const at_attributes = [
                  { name: AuditTrailFields.REGISTRATION_CODE, value: 'VER_BIO' },
                ]
                this.auditTrailService.registerAuditTrailAction(this.userNumId, AuditTrailActions.LOGIN_BIO, 0, 'SUCCESS', event.data.tech, at_attributes);
                this.bioAuth(event.data.token, this.userNumId);
              }
              else {
                const at_attributes = [
                  { name: AuditTrailFields.REGISTRATION_CODE, value: 'VER_BIO' },
                  { name: AuditTrailFields.ERROR_DESCRIPTION, value: 'BIOMETRIC LOGIN USE CASE ERROR' },
                ]
                this.auditTrailService.registerAuditTrailAction(this.userNumId, AuditTrailActions.LOGIN_BIO, 0, 'ERROR', event.data.tech, at_attributes);
                this.authError({ errorCode: '', numId: this.userNumId }, true);
              }
            },
            (e) => {
              this.loggerService.error("Users MS")
              this.loggerService.error(e);
              const at_attributes = [
                { name: AuditTrailFields.REGISTRATION_CODE, value: 'VER_BIO' },
                { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              ]
              this.auditTrailService.registerAuditTrailAction(this.userNumId, AuditTrailActions.LOGIN_BIO, 0, getAuthError(e.code.toString()), event.data.tech, at_attributes);
              this.authError({ errorCode: e.code.toString(), numId: this.userNumId }, true);
            }
          );
        }
        this.showWidgetDialog = false;
        break;
      case "search":
        if (event.data.nId && event.data.token) {
          this.localStorageService.setUserVerified(event.data.token);
          this.localStorageService.saveToken(event.data.token);
          const at_attributes = [
            { name: AuditTrailFields.REGISTRATION_CODE, value: 'IDN_BIO' },
          ]
          this.auditTrailService.registerAuditTrailAction(event.data.nId, AuditTrailActions.LOGIN_BIO, 0, 'SUCCESS', event.data.tech, at_attributes);
          this.bioAuth(event.data.token, event.data.nId);
        }
        else {
          this.messageService.add(
            {
              severity: 'error',
              summary: this.translateService.instant("titles.unauthorized"),
              detail: `${this.translateService.instant("messages.unauthorized")}`
            },
          );
        }
        this.showWidgetSearchDialog = false;
        break
      case "process":
        break;
      case "close_verify":
      case "close_search":
      case "error":
        this.localStorageService.setUserVerified("");
        this.showWidgetSearchDialog = false;
        this.showWidgetDialog = false;
        break;
    }
  }

  bioAuth(token: string, userNumId: string) {
    this.isLoading = true;
    this.refreshByUserUseCase.execute({ oldToken: token }).then(
      (data) => {
        if (data && data.token) {
          this.localStorageService.saveToken(data.token);
          this.getSettingsByApplicationUseCase.execute({ applicationName: this.application }).then(
            (data) => {
              if (data.settings) {
                this.localStorageService.setSessionSettings(data.settings);
                this.loginNumId(userNumId);
              }
              else {
                this.loggerService.error("System Settings MS")
                this.messageService.add({
                  severity: 'error',
                  summary: this.translateService.instant("titles.systemSettings"),
                  detail: `${this.translateService.instant("messages.error_login")}`,
                  life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.isLoading = false;
              }
            },
            (e) => {
              this.loggerService.error("System Settings MS")
              this.loggerService.error(e);
              this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant("titles.systemSettings"),
                detail: `${this.translateService.instant("messages.error_login")}`,
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
              });
              this.isLoading = false;
            },
          );
        }
      },
      (e) => {
        this.loggerService.error("Auth MS")
        this.loggerService.error(e);
        this.messageService.add({
          severity: 'error',
          summary: this.translateService.instant("titles.auth"),
          detail: `${this.translateService.instant("messages.error_login")}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.isLoading = false;
      }
    );
  }

  loginNumId(numId: string) {
    this.getUserByNumIdUseCase.execute({ numId: numId }).then(
      (data) => {
        if (data) {
          this.auditTrailService.registerAuditTrailAction(data.numId!, AuditTrailActions.LOGIN, 0, 'SUCCESS', this.technology, []);
          this.receiveSubject(data)
        }
      },
      (e) => {
        this.loggerService.error("Users MS")
        this.loggerService.error(e);
        this.messageService.add({
          severity: 'error',
          summary: this.translateService.instant("titles.error_login"),
          detail: `${this.translateService.instant("messages.error_login")}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      })
      .finally(() => {
        this.isLoading = false;
      });
  }

  onResetPassword(event: { token: string }) {
    this.token = event.token;
    this.showResetPassword = true;
  }

  // Getting the result of reset password
  resetPasswordResult(resetStatus: boolean) {
    if (!resetStatus) {
      this.messageService.add(
        {
          severity: 'error',
          summary: this.translateService.instant("content.errorTitle"),
          detail: `${this.translateService.instant("messages.error_resetting_password")}`
        },
      );
    }
    this.messageService.add(
      {
        severity: 'success',
        summary: this.translateService.instant("content.successTitle"),
        detail: `${this.translateService.instant("messages.password_reset_email_sent")}`,
        life: this.managerSettings?.timeoutMessages ? this.managerSettings.timeoutMessages * 1000 : 5000
      },
    );
    this.showResetPassword = false;
  }

  cancelResetPasswordResult(cancelStatus: boolean) {
    this.showResetPassword = false;
  }

  passwordRecoveryResult(recoveryStatus: boolean) {
    if (!recoveryStatus) {
      this.messageService.add(
        {
          severity: 'error',
          summary: this.translateService.instant("content.errorTitle"),
          detail: `${this.translateService.instant("messages.error_updating_password")}`
        },
      );
    }
    else {
      this.messageService.add(
        {
          severity: 'success',
          summary: this.translateService.instant("content.successTitle"),
          detail: `${this.translateService.instant("messages.password_updated")}`
        },
      );
    }
    this.showPasswordRecovery = false;
  }

  cancelPasswordRecovery() {
    this.showPasswordRecovery = false;
  }

  getCustomLoginLogo() {
    if (this.managerSettings?.continued1?.customPartnerLogo?.customLoginLogoEnabled) {
      const id = this.managerSettings?.continued1?.customPartnerLogo?.customLoginLogoRawStaticResourceId;
      const type = this.managerSettings?.continued1?.customPartnerLogo?.customLoginLogoType;
      if (id != "" && id != null && id != undefined) {
        this.getRawStaticResourceByIdUseCase.execute({ id: id, token: this.token }).then(
          (data) => {
              // this.loggerService.log("Obtained the raw static resource with id: " + id);
              // this.loggerService.log(data);
              if (data && data.content) {
                this.customLoginLogo = true;
                this.customLoginLogoSource = 'data:' + type + ';base64,' + data.content;
                let loginLogo = data;
                loginLogo.content = this.customLoginLogoSource;
                // this.localStorageService.setItem('customLoginLogo', JSON.stringify(loginLogo));
              }
          },
          (e) => {
              this.loggerService.error("Storage MS");
              this.loggerService.error(e);
          }
      );
      }
    }
  }
}