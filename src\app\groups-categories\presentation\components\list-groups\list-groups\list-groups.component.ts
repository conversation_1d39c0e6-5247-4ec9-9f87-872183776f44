import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { MessageService, ConfirmationService, FilterService } from 'primeng/api';
import { Table } from 'primeng/table';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { OperationType } from 'src/verazial-common-frontend/core/general/assignment/categories/common/enum/operation-type.enum';
import { GroupCategoryType } from 'src/verazial-common-frontend/core/general/assignment/categories/common/models/group-category-type.enum';
import { GroupCategoryEntity } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/entity/group-category.entity';
import { DeleteGroupCategoryByIdUseCase } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/use-cases/delete-group-category-by-id.use-case';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { OperationStatus } from 'src/verazial-common-frontend/core/models/operation-status.interface';
import { Status } from 'src/verazial-common-frontend/core/models/status.enum';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'app-list-groups',
  templateUrl: './list-groups.component.html',
  styleUrl: './list-groups.component.css',
  providers: [MessageService, ConfirmationService]
})
export class ListGroupsComponent implements OnInit, OnDestroy {

  @Input() groups: GroupCategoryEntity[] = [];
  @Input() readAndWritePermissions: boolean = false;
  @Output() updateGroups = new EventEmitter<boolean>();

  data: any[] = [];
  showNewGroupDialog: boolean = false;
  groupData: GroupCategoryEntity | undefined;
  operationType: OperationType | undefined;

  searchValue: string | undefined;

  // Date Range Filter
  formGroup: FormGroup = new FormGroup({
    date: new FormControl<Date[] | null>(null)
  });
  formGroupDate: FormGroup = new FormGroup({
    date: new FormControl<Date[] | null>(null)
  });
  dateFilterValues = {
    startDate: null,
    endDate: null
  };
  rangeDates: Date[] | null = null;

  confirmDialogTimeoutLimit: number = 0;
  startCheckingInactivity: boolean = false;

  constructor(
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private translateService: TranslateService,
    private deleteGroupCategoryByIdUseCase: DeleteGroupCategoryByIdUseCase,
    private filterService: FilterService,
    private localStorageService: LocalStorageService,
    private auditTrailService: AuditTrailService,
    private loggerService: ConsoleLoggerService,
  ) {
    this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
      if (!filter || (!filter.startDate && !filter.endDate)) {
        return true; // If no filter, show all
      }
      const dateValue = new Date(value).getTime();
      const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
      const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
      if (startDate && endDate) {
        return dateValue >= startDate && dateValue <= endDate;
      } else if (startDate) {
        return dateValue >= startDate;
      } else if (endDate) {
        return dateValue <= endDate;
      }
      return false;
    });
  }

  ngOnInit(): void {
  }

  ngOnDestroy(): void {
    this.groupData = undefined;
    // Clean up the timeout if the component is destroyed
    this.closeConfirmationDialog();
  }

  resetInactivityMonitor() {
    this.startCheckingInactivity = false;
    this.confirmDialogTimeoutLimit = 0;
  }

  deleteGroup(data: GroupCategoryEntity) {
    this.confirmationService.confirm({
      message: this.translateService.instant('messages.message_remove') + " <b>" + data.name + `</b>?`,
      header: this.translateService.instant('groups.delete_group'),
      icon: 'pi pi-exclamation-triangle',
      acceptIcon: "none",
      rejectIcon: "none",
      rejectButtonStyleClass: "p-button-text",
      acceptButtonStyleClass: "ng-confirm-button",
      acceptLabel: this.translateService.instant('delete'),
      rejectLabel: this.translateService.instant('no'),
      accept: () => {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
        ];
        this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.DEL_CAT, ReasonActionTypeEnum.DELETE, () => { this.deleteGroupById(data); }, at_attributes);
      },
      reject: () => {
        this.resetInactivityMonitor();
      }
    });

    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }

  createNewGroup() {
    this.groupData = undefined;
    this.showNewGroupDialog = true;
    this.operationType = OperationType.INSERT;
  }

  editGroup(group: GroupCategoryEntity) {
    this.groupData = group;
    this.showNewGroupDialog = true;
    this.operationType = OperationType.UPDATE;
  }

  deleteGroupById(data: GroupCategoryEntity) {
    const id = data.id!!;
    this.deleteGroupCategoryByIdUseCase.execute({ id: id }).then(
      (data) => {
        this.groups = [...this.groups.filter((_v, k) => _v.id != id)];
        this.messageService.add({ severity: 'success', summary: this.translateService.instant("titles.success_operation"), detail: this.translateService.instant("messages.success_general"),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000 });
      },
      (e) => {
        this.messageService.add({ severity: 'error', summary: this.translateService.instant("titles.error_operation"), detail: `${this.translateService.instant("messages.error_deleting_category")}: ${e.message}`,
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000 });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_CAT, 0, 'ERROR', '', at_attributes);
      })
  }

  operationStatus(event: OperationStatus) {
    if (event.status == Status.SUCCESS) {
      this.messageService.add({ severity: 'success', summary: this.translateService.instant("titles.success_operation"), detail: this.translateService.instant("messages.success_general"),
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000 });
      this.showNewGroupDialog = false;
      this.updateGroups.emit(true);
    } else {
      this.messageService.add({ severity: 'error', summary: this.translateService.instant("titles.error_operation"), detail: `${event.message}`,
      life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000 });
    }
  }

  getCategoryTranslation(type: GroupCategoryType) {
    let translation: string = "";
    switch (type) {
      case GroupCategoryType.USERS:
        translation = this.translateService.instant('content.subject');
        break;
      case GroupCategoryType.LOCATIONS:
        translation = this.translateService.instant('content.location');
        break;
      case GroupCategoryType.SCHEDULES:
        translation = this.translateService.instant('content.schedule');
        break
    }
    return translation
  }

  getIcon(categoryType: string): string {
    const type = parseInt(categoryType);
    let icon: string = "cog";
    switch (type) {
      case GroupCategoryType.USERS:
        icon = "users";
        break;
      case GroupCategoryType.LOCATIONS:
        icon = "map-marker";
        break;
      case GroupCategoryType.SCHEDULES:
        icon = "clock";
        break;
      default:
        icon = "cog";
    }
    return icon;
  }

  getColour(categoryType: string): string {
    const type = parseInt(categoryType);
    let colour: string = "#295BAC";
    switch (type) {
      case GroupCategoryType.USERS:
        colour = "#cce5ff";
        break;
      case GroupCategoryType.LOCATIONS:
        colour = "#d4edda";
        break;
      case GroupCategoryType.SCHEDULES:
        colour = "#fff3cd";
        break;
      default:
        colour = "#295BAC";
    }
    return colour;
  }

  getBackgroundColour(categoryType: string): string {
    const type = parseInt(categoryType);
    let colour: string = "#ABC9FB";
    switch (type) {
      case GroupCategoryType.USERS:
        colour = "rgb(0, 123, 255)";
        break;
      case GroupCategoryType.LOCATIONS:
        colour = "rgb(40, 167, 69)";
        break;
      case GroupCategoryType.SCHEDULES:
        colour = "rgb(223, 169, 10)";
        break;
      default:
        colour = "#ABC9FB";
    }
    return colour;
  }

  /* Search */
  onFilter(event: any, dt: Table) {
    if(!event.filters['createdAt'].value){
      this.rangeDates = null;
      this.formGroup.reset();
    }
    if(!event.filters['updatedAt'].value){
      this.rangeDates = null;
      this.formGroupDate.reset();
    }
  }

  /* Date Range Filter */
  applyDateRangeFilter(dt: Table, field: string) {
    if(field === 'createdAt'){
      this.rangeDates = this.formGroup.get('date')?.value;
    }
    else if(field === 'updatedAt'){
      this.rangeDates = this.formGroupDate.get('date')?.value;
    }
    dt.filter({
      startDate: this.rangeDates ? this.rangeDates[0] : null,
      endDate: this.rangeDates ? this.rangeDates[1] : null
    }, field, 'customDateRange');
  }

  closeConfirmationDialog() {
    this.confirmationService.close();
    this.resetInactivityMonitor();
  }
}
