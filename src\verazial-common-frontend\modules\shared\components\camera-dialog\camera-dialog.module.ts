import { NgModule } from '@angular/core';
import { CameraDialogComponent } from './camera-dialog/camera-dialog.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { AccordionModule } from 'primeng/accordion';
import { CarouselModule } from 'primeng/carousel';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { FloatLabelModule } from 'primeng/floatlabel';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { InactivityMonitorModule } from '../inactivity-monitor/inactivity-monitor.module';

@NgModule({
  declarations: [
    CameraDialogComponent
  ],
  imports: [
    /* Angular Modules */
    CommonModule,
    /* Forms */
    ReactiveFormsModule,
    FormsModule,
    /* Translate */
    TranslateModule,
    /* PrimeNG Modules */
    DialogModule,
    ButtonModule,
    DropdownModule,
    ProgressSpinnerModule,
    FloatLabelModule,
    ConfirmDialogModule,
    ToastModule,
    /* Custom Modules */
    InactivityMonitorModule,
  ],
  exports: [
    CameraDialogComponent
  ]
})
export class CameraDialogModule { }
