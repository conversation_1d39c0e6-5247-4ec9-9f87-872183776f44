import { NgModule } from "@angular/core";
import { BelongingsListComponent } from "./belongings-list/belongings-list.component";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { ProgressSpinnerModule } from "primeng/progressspinner";
import { DialogModule } from "primeng/dialog";
import { ButtonModule } from "primeng/button";
import { TableModule } from "primeng/table";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { DropdownModule } from "primeng/dropdown";
import { InputSwitchModule } from "primeng/inputswitch";
import { CalendarModule } from "primeng/calendar";
import { InputTextareaModule } from "primeng/inputtextarea";
import { AccordionModule } from "primeng/accordion";
import { ScrollPanelModule } from "primeng/scrollpanel";
import { CarouselModule } from "primeng/carousel";
import { CameraModule } from "src/verazial-common-frontend/modules/shared/components/camera/camera.module";
import { WidgetSearchModule } from "src/verazial-common-frontend/modules/shared/components/widget-search/widget-search.module";
import { WidgetMatchModule } from "src/verazial-common-frontend/modules/shared/components/widget-match/widget-match.module";
import { FloatLabelModule } from "primeng/floatlabel";
import { BioTechButtonsModule } from "src/verazial-common-frontend/modules/shared/components/bio-tech-buttons/bio-tech-buttons.module";
import { EmptyModule } from "src/verazial-common-frontend/modules/shared/components/empty/empty.module";
import { StepsModule } from "primeng/steps";
import { SelectButtonModule } from "primeng/selectbutton";
import { TooltipModule } from "primeng/tooltip";
import { ProfilePicModule } from "src/verazial-common-frontend/modules/shared/components/profile-pic/profile-pic.module";
import { BioSignaturesModule } from "src/verazial-common-frontend/modules/shared/components/bio-signatures/bio-signatures.module";
import { InactivityMonitorModule } from "src/verazial-common-frontend/modules/shared/components/inactivity-monitor/inactivity-monitor.module";

@NgModule({
    declarations: [
      BelongingsListComponent,
    ],
    imports: [
      /* Angular Modules */
      CommonModule,
      /* Forms */
      ReactiveFormsModule,
      FormsModule,
      /* Translate */
      TranslateModule,
      /* PrimeNG Modules */
      ProgressSpinnerModule,
      DialogModule,
      ButtonModule,
      TableModule,
      IconFieldModule,
      InputIconModule,
      InputTextModule,
      InputTextareaModule,
      ConfirmDialogModule,
      DropdownModule,
      InputSwitchModule,
      CalendarModule,
      AccordionModule,
      ScrollPanelModule,
      CarouselModule,
      FloatLabelModule,
      StepsModule,
      SelectButtonModule,
      TooltipModule,
      /* Custom Modules */
      CameraModule,
      WidgetMatchModule,
      WidgetSearchModule,
      BioTechButtonsModule,
      EmptyModule,
      ProfilePicModule,
      BioSignaturesModule,
      InactivityMonitorModule,
    ],
    exports: [
      BelongingsListComponent
    ]
  })
  export class BelongingsModule { }