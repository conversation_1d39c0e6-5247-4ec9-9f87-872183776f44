import {Injectable, On<PERSON><PERSON>roy} from '@angular/core';
import {ConsoleLoggerService} from './console-logger.service';
import {capitalizeFirstLetter, getTechId} from '../util/supporting-functions';
import {environment} from 'src/environments/environment';
import {GetKonektorPropertiesUseCase} from '../general/konektor/domain/use-cases/get-konektor-properties.use-case';
import {KonektorPropertiesModel} from '../general/konektor/data/model/konektor-properties.model';
import {LocalStorageService} from './local-storage.service';
import {ConfirmationService, MessageService} from 'primeng/api';
import {TranslateService} from '@ngx-translate/core';
import {FormControl, FormGroup} from '@angular/forms';
import {AuditTrailFields} from '../models/audit-trail-fields.enum';
import {NewAction} from '../generated/actionsV2/actions_pb';
import {Struct, Value} from 'google-protobuf/google/protobuf/struct_pb';
import {Timestamp} from 'google-protobuf/google/protobuf/timestamp_pb';
import {RoleEntity} from '../general/common/entity/role.entity';
import {RegisterActionUseCase} from '../general/actionsV2/domain/use-cases/register-action.use-case';
import {ExtraData} from '../general/actionsV2/domain/entity/extra-data.interface';
import {EntriesExitsEntity} from '../general/subject/domain/entity/entries-exits.entity';
import {AuthenticateByLicenseUseCase} from '../general/auth/domain/use-cases/authenticate-by-license.use-case';
import {AuthByLicenseRequestEntity} from '../general/auth/domain/entity/auth-by-license-request.entity';
import {ActionEntity} from '../general/actionsV2/domain/entity/action.entity';
import {
  LanguageRecordModel,
  TranslationGroup,
  TranslationModel
} from '../general/manager/common/models/translation.model';

export enum ReasonTypeEnum {
  USER,
  SUBJECT,
  CONFIG,
  EXPORT_REPORT,
  ROLE_MANAGEMENT,
  PASS_APP_MANAGEMENT,
  LICENSE,
  CRIMINALISTIC,
  OTHER,
}

export enum ReasonActionTypeEnum {
  DELETE,
  UPDATE,
  CREATE
}

export interface DropdownOptions {
  name: string;
  code: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuditTrailService implements OnDestroy {

  /* Audit Trail */
  public reasonOptions?: DropdownOptions[];
  public reasonsForm: FormGroup = new FormGroup({
    reason: new FormControl(''),
  });

  rejectTimeout: any;

  public lastReason: string = '';

  constructor(
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService,
    private translateService: TranslateService,
    private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
    private registerActionUseCase: RegisterActionUseCase,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private authenticateByLicenseUseCase: AuthenticateByLicenseUseCase,
  ) { }

  ngOnDestroy() {
    // Clean up the timeout if the component is destroyed
    this.clearRejectTimeout();
  }

  async registerAction(action: NewAction, konektorProperties: KonektorPropertiesModel): Promise<void> {
    try {
      const token = this.localStorageService.getToken();

      if (token) {
        const data = await this.registerActionUseCase.execute({ newAction: action });
        this.loggerService.debug('Audit Trail action saved successfully.');
        this.loggerService.debug(data);
        return;
      }

      if (!konektorProperties.license) {
        this.loggerService.error("Konektor Service:");
        this.loggerService.error("  No License Configured.");
        this.loggerService.error("  Cannot register action.");
        return;
      }

      const authByLicenseRequest: AuthByLicenseRequestEntity = {
        license: konektorProperties.license,
        deviceHash: konektorProperties.deviceHash,
        applicationName: environment.application,
      };

      const authResponse = await this.authenticateByLicenseUseCase.execute(authByLicenseRequest);

      if (!authResponse || !authResponse.token) {
        this.loggerService.error("Auth MS");
        this.loggerService.error("No Token");
        this.loggerService.error("Cannot register action.");
        return;
      }

      const data = await this.registerActionUseCase.execute({ newAction: action, token: authResponse.token });
      this.loggerService.debug('Audit Trail action saved successfully.');
      this.loggerService.debug(data);

    } catch (e) {
      this.loggerService.error('Error saving Audit Trail action:');
      this.loggerService.error(e);
    }
  }

  registerActionPromise(action: NewAction, konektorProperties: KonektorPropertiesModel): Promise<ActionEntity> {
    return new Promise((resolve, reject) => {
      if (this.localStorageService.getToken()) {
        this.registerActionUseCase.execute({ newAction: action }).then(
          (data) => {
            this.loggerService.debug('Audit Trail action saved successfully.');
            this.loggerService.debug(data);
            resolve(data);
          },
          (e) => {
            this.loggerService.error('Error saving Audit Trail action:');
            this.loggerService.error(e);
            reject(e);
          }
        );
      } else {
        if (!konektorProperties.license) {
          this.loggerService.error("Konektor Service:")
          this.loggerService.error("  No License Configured.")
          this.loggerService.error("  Cannot register action.")
          reject(new Error("No License Configured"));
          return;
        }

        let authByLicenseRequest: AuthByLicenseRequestEntity = {
          license: konektorProperties.license,
          deviceHash: konektorProperties.deviceHash,
          applicationName: environment.application,
        };

        this.authenticateByLicenseUseCase.execute(authByLicenseRequest).then(
          (data) => {
            if (data) {
              const token = data.token!;
              this.registerActionUseCase.execute({ newAction: action, token }).then(
                (data) => {
                  this.loggerService.debug('Audit Trail action saved successfully.');
                  this.loggerService.debug(data);
                  resolve(data);
                },
                (e) => {
                  this.loggerService.error('Error saving Audit Trail action:');
                  this.loggerService.error(e);
                  reject(e);
                }
              );
            } else {
              this.loggerService.error("Auth MS")
              this.loggerService.error("No Token")
              this.loggerService.error("Cannot register action.")
              reject(new Error("No Token Available"));
            }
          },
          (e) => {
            this.loggerService.error("Auth MS")
            this.loggerService.error("Cannot register action.")
            this.loggerService.error(e);
            reject(e);
          }
        );
      }
    });
  }

  private checkIfActionAvailable(actionId: string): boolean {
    // Check if the Audit Trail Configuration is enabled
    let actionEnabled = false;
    const managerSettings = this.localStorageService.getSessionSettings();
    if (managerSettings && managerSettings.auditTrailConfig && managerSettings.auditTrailConfig.length > 0) {
      managerSettings.auditTrailConfig.forEach((auditTrail) => {
        if (auditTrail.code == actionId && auditTrail.isEnable) {
          actionEnabled = true;
        }
      });
    }
    return actionEnabled;
  }

  public registerAuditTrailAction(
    numId: string,
    actionId: string,
    actionDuration: number,
    actionResult: string,
    technologyId: string,
    attributes: ExtraData[],
    checkActionAvailable: boolean = true
  ): Promise<void> {
    if (checkActionAvailable && !this.checkIfActionAvailable(actionId)) {
      return Promise.resolve(); // Skip if not available
    }

    return new Promise((resolve, reject) => {
      this.getKonektorPropertiesUseCase.execute().subscribe({
        next: async (data) => {
          if (data.apiGatewayGrpc) {
            this.localStorageService.setApiGatewayURL(data.apiGatewayGrpc);
          }
          else {
            this.localStorageService.destroyApiGatewayURL();
          }
          const konektorProperties: KonektorPropertiesModel = data!;
          await this.registerAction(
            this.buildNewActionData(numId, actionId, actionDuration, actionResult, technologyId, attributes, konektorProperties),
            konektorProperties
          );
          resolve();
        },
        error: (e) => {
          this.loggerService.error('Audit Trail: Error retrieving Konektor Properties:');
          this.loggerService.error(e);
          reject(e);
        }
      });
    });
  }

  public registerEntryExitAuditTrailAction(entryexit: EntriesExitsEntity): Promise<ActionEntity | undefined> {
    return new Promise((resolve) => {
      this.getKonektorPropertiesUseCase.execute().subscribe({
        next: (data) => {
          if (data.apiGatewayGrpc) {
            this.localStorageService.setApiGatewayURL(data.apiGatewayGrpc);
          }
          else {
            this.localStorageService.destroyApiGatewayURL();
          }
          this.loggerService.debug('Audit Trail: Konektor settings retrieved successfully');
          const konektorProperties: KonektorPropertiesModel = data!;

          let newAction = new NewAction();

          const reportedAt = new Timestamp();
          const now = new Date();

          reportedAt.setSeconds(Math.floor(now.getTime() / 1000));
          reportedAt.setNanos((now.getTime() % 1000) * 1e6);
          newAction.setReportedAt(reportedAt);

          const dataStruct = new Struct();
          const dataFieldsMap = dataStruct.getFieldsMap();

          dataFieldsMap.set(AuditTrailFields.APPLICATION_ID, new Value().setStringValue(capitalizeFirstLetter(environment.application.toLowerCase())));
          dataFieldsMap.set(AuditTrailFields.APP_VERSION, new Value().setStringValue(environment.version));
          dataFieldsMap.set(AuditTrailFields.ACTION_NAME, new Value().setStringValue(entryexit.type!));

          const commonAttributes = new Struct();
          const commonAttributesMap = commonAttributes.getFieldsMap();

          const roles: string[] = this.localStorageService.getUser()?.roles?.map((role: RoleEntity) => role.name) || [];

          commonAttributesMap.set(AuditTrailFields.EXECUTOR_ID, new Value().setStringValue(entryexit.executorId!));
          commonAttributesMap.set(AuditTrailFields.EXECUTOR_PROFILE, new Value().setStringValue(roles.join(',')));
          commonAttributesMap.set(AuditTrailFields.RECEIVER_ID, new Value().setStringValue(entryexit.subjectId!));
          commonAttributesMap.set(AuditTrailFields.LOCATION_ID, new Value().setStringValue(konektorProperties.locationId ?? ""));
          commonAttributesMap.set(AuditTrailFields.SEGMENT_ID, new Value().setStringValue(konektorProperties.segmentId ?? ""));
          commonAttributesMap.set(AuditTrailFields.DEVICE_ID, new Value().setStringValue(konektorProperties.deviceId ?? ""));
          commonAttributesMap.set(AuditTrailFields.ACTION_RESULT, new Value().setStringValue("SUCCESS"));

          dataFieldsMap.set("commonAttributes", new Value().setStructValue(commonAttributes));

          const timmingAttributes = new Struct();
          const timmingAttributesMap = timmingAttributes.getFieldsMap();
          timmingAttributesMap.set("totalTime", new Value().setNumberValue(0));

          dataFieldsMap.set("timmingAttributes", new Value().setStructValue(timmingAttributes));

          const entryExitAttributes = new Struct();
          const entryExitAttributesMap = entryExitAttributes.getFieldsMap();
          entryExitAttributesMap.set(AuditTrailFields.ACTION_TYPE, new Value().setStringValue("EntryPrisons"));
          entryExitAttributesMap.set(AuditTrailFields.ENTRY_DATE, new Value().setStringValue(entryexit.entryDate?.toISOString() ?? ""));
          if (entryexit.reason)
            entryExitAttributesMap.set(AuditTrailFields.REASON, new Value().setStringValue(entryexit.reason));
          if (entryexit.description)
            entryExitAttributesMap.set(AuditTrailFields.DESCRIPTION, new Value().setStringValue(entryexit.description));
          entryExitAttributesMap.set(AuditTrailFields.LOCATION_ID, new Value().setStringValue(entryexit.locationId!));
          entryExitAttributesMap.set(AuditTrailFields.TYPE, new Value().setStringValue(entryexit.type!));
          entryExitAttributesMap.set(AuditTrailFields.SUBJECT_ID, new Value().setStringValue(entryexit.subjectId!));
          entryExitAttributesMap.set(AuditTrailFields.EXECUTOR_ID, new Value().setStringValue(entryexit.executorId!));
          entryExitAttributesMap.set(AuditTrailFields.ROLE_ID, new Value().setStringValue(entryexit.roleId!));

          if (entryexit.relatedSubject)
            entryExitAttributesMap.set(AuditTrailFields.RELATED_SUBJECT_ID, new Value().setStringValue(entryexit.relatedSubject));

          if (entryexit.userSignatureTech)
            entryExitAttributesMap.set(AuditTrailFields.USER_SIGNATURE_TECH, new Value().setStringValue(entryexit.userSignatureTech));
          if (entryexit.userSignatureNumId)
            entryExitAttributesMap.set(AuditTrailFields.USER_SIGNATURE_NUM_ID, new Value().setStringValue(entryexit.userSignatureNumId));
          if (entryexit.userSignatureDate)
            entryExitAttributesMap.set(AuditTrailFields.USER_SIGNATURE_DATE, new Value().setStringValue(entryexit.userSignatureDate?.toISOString() ?? ""));
          if (entryexit.subjectSignatureTech)
            entryExitAttributesMap.set(AuditTrailFields.SUBJECT_SIGNATURE_TECH, new Value().setStringValue(entryexit.subjectSignatureTech));
          if (entryexit.subjectSignatureDate)
            entryExitAttributesMap.set(AuditTrailFields.SUBJECT_SIGNATURE_DATE, new Value().setStringValue(entryexit.subjectSignatureDate?.toISOString() ?? ""));
          if (entryexit.maxTimeVisit)
            entryExitAttributesMap.set(AuditTrailFields.MAX_TIME_VISIT, new Value().setNumberValue(entryexit.maxTimeVisit));
          if (entryexit.actualTimeVisit)
            entryExitAttributesMap.set(AuditTrailFields.ACTUAL_TIME_VISIT, new Value().setNumberValue(entryexit.actualTimeVisit));
          if (entryexit.transferLimitDate)
            entryExitAttributesMap.set(AuditTrailFields.TRANSFER_LIMIT_DATE, new Value().setStringValue(entryexit.transferLimitDate?.toISOString() ?? ""));
          if (entryexit.transferId)
            entryExitAttributesMap.set(AuditTrailFields.TRANSFER_ID, new Value().setStringValue(entryexit.transferId));
          if (entryexit.destinyLocationId)
            entryExitAttributesMap.set(AuditTrailFields.DESTINY_LOCATION_ID, new Value().setStringValue(entryexit.destinyLocationId));
          if (entryexit.entryExitAuthId)
            entryExitAttributesMap.set(AuditTrailFields.ENTRY_EXIT_AUTH_ID, new Value().setStringValue(entryexit.entryExitAuthId));
          if (entryexit.entryExitAuthLimitDate)
            entryExitAttributesMap.set(AuditTrailFields.ENTRY_EXIT_AUTH_LIMIT_DATE, new Value().setStringValue(entryexit.entryExitAuthLimitDate?.toISOString() ?? ""));

          dataFieldsMap.set("entryExitAttributes", new Value().setStructValue(entryExitAttributes));

          newAction.setData(dataStruct);

          this.registerActionUseCase.execute({ newAction }).then(
            (data) => {
              this.loggerService.debug('Audit Trail action saved successfully');
              this.loggerService.debug(data);
              resolve(data);
            },
            (e) => {
              this.loggerService.error('Error saving Audit Trail action:');
              this.loggerService.error(e);
              resolve(undefined);
            }
          );
        },
        error: (e) => {
          this.loggerService.error('Audit Trail: Error retrieving Konektor Properties:');
          this.loggerService.error(e);
          resolve(undefined);
        }
      });
    });
  }

  /* Audit Trail Functions */
  public auditTrailSelectReason(reasonType: ReasonTypeEnum, actionId: string, actionType: ReasonActionTypeEnum, _callback: Function, attributes: ExtraData[] = [], register: boolean = true, reasons: string[] = [], translations: TranslationModel[] = [], executorNumId: string = ''): void {
    if (!this.checkIfActionAvailable(actionId) && reasons.length == 0) {
      _callback();
      return;
    }
    const managerSettings = this.localStorageService.getSessionSettings();
    const userManagementReasons = managerSettings?.userManagementReasons || [];
    const userManagementReasonsTranslations = managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'userManagementReasons')?.translations || [];
    const subjectManagementReasons = managerSettings?.biographicDataManagementReasons || [];
    const subjectManagementReasonsTranslations = managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'biographicDataManagementReasons')?.translations || [];
    const configManagementReasons = managerSettings?.configManagementReasons || [];
    const configManagementReasonsTranslations = managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'configManagementReasons')?.translations || [];
    const exportReportReasons = managerSettings?.continued1?.exportReportReasons || [];
    const exportReportReasonsTranslations = managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'exportReportReasons')?.translations || [];
    const roleManagementReasons = managerSettings?.continued1?.roleManagementReasons || [];
    const roleManagementReasonsTranslations = managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'roleManagementReasons')?.translations || [];
    const passAppManagementReasons = managerSettings?.continued1?.passAppManagementReasons || [];
    const passAppManagementReasonsTranslations = managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'passAppManagementReasons')?.translations || [];
    const licenseManagementReasons = managerSettings?.continued1?.licenseManagementReasons || [];
    const licenseManagementReasonsTranslations = managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'licenseManagementReasons')?.translations || [];


    // this.loggerService.debug('Audit Trail: Available reasons for selection:');
    // this.loggerService.debug(licenseManagementReasonsTranslations);
    // this.loggerService.debug(licenseManagementReasons);

    // this.loggerService.debug(managerSettings?.continued1);

    let reasonOptionsToUse: string[] = [];
    let translationsToUse: TranslationModel[] = [];
    reasonOptionsToUse = reasons;
    translationsToUse = translations;
    if (reasonOptionsToUse.length == 0) {
      switch (reasonType) {
        case ReasonTypeEnum.USER:
          reasonOptionsToUse = userManagementReasons;
          translationsToUse = userManagementReasonsTranslations;
          break;
        case ReasonTypeEnum.SUBJECT:
          reasonOptionsToUse = subjectManagementReasons;
          translationsToUse = subjectManagementReasonsTranslations;
          break;
        case ReasonTypeEnum.CONFIG:
          reasonOptionsToUse = configManagementReasons;
          translationsToUse = configManagementReasonsTranslations;
          break;
        case ReasonTypeEnum.EXPORT_REPORT:
          reasonOptionsToUse = exportReportReasons;
          translationsToUse = exportReportReasonsTranslations;
          break;
        case ReasonTypeEnum.ROLE_MANAGEMENT:
          reasonOptionsToUse = roleManagementReasons;
          translationsToUse = roleManagementReasonsTranslations;
          break;
        case ReasonTypeEnum.PASS_APP_MANAGEMENT:
          reasonOptionsToUse = passAppManagementReasons;
          translationsToUse = passAppManagementReasonsTranslations;
          break;
        case ReasonTypeEnum.LICENSE:
          // this.loggerService.debug('Audit Trail: License Management Reasons');
          reasonOptionsToUse = licenseManagementReasons;
          translationsToUse = licenseManagementReasonsTranslations;
          break;
        case ReasonTypeEnum.CRIMINALISTIC:

        case ReasonTypeEnum.OTHER:
          reasonOptionsToUse = reasons;
          translationsToUse = translations;
          break;
      }
    }
    let executor: string = executorNumId != '' ? executorNumId : this.localStorageService.getUser().numId
    this.reasonOptions = reasonOptionsToUse.map((reason: string) => {
      return {
        name: translationsToUse.find((t: TranslationModel) => t.key === reason)?.translations?.find((t: LanguageRecordModel) => t.languageCode == this.translateService.currentLang)?.value || reason,
        code: reason
      };
    });
    let messageDesc = '';
    switch (actionType) {
      case ReasonActionTypeEnum.DELETE:
        messageDesc = 'table.reasonDelete';
        break;
      case ReasonActionTypeEnum.UPDATE:
        messageDesc = 'table.reasonUpdate';
        break;
      case ReasonActionTypeEnum.CREATE:
        messageDesc = 'table.reasonCreate';
    }
    if (this.reasonOptions.length > 0) {
      let selectedReason = '';
      if (this.reasonOptions.length === 1) {
        attributes.push({ name: AuditTrailFields.REASON, value: this.reasonOptions[0].code });
        this.lastReason = this.reasonOptions[0].code;
        if (register)
          this.registerAuditTrailAction(executor, actionId, 0, 'SUCCESS', '', attributes, reasons.length > 0 ? false : true);
        _callback();
      }
      else {
        this.confirmationService.confirm({
          header: this.translateService.instant('content.reason'),
          message: this.translateService.instant(messageDesc),
          key: 'auditTrailSelectReasonDialog',
          accept: () => {
            this.clearRejectTimeout();
            const reason = this.reasonsForm.get('reason')?.value;
            selectedReason = reason;
            if (selectedReason) {
              attributes.push({ name: AuditTrailFields.REASON, value: selectedReason });
              this.lastReason = selectedReason;
              if (register) {
                this.registerAuditTrailAction(executor, actionId, 0, 'SUCCESS', '', attributes, reasons.length > 0 ? false : true);
              }
              this.reasonOptions = [];
              this.reasonsForm.reset();
              _callback();
            }
            else {
              this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('titles.error_operation'),
                detail: this.translateService.instant('messages.must_select_reason'),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
              });
            }
          },
          reject: () => {
            this.clearRejectTimeout();
            selectedReason = '';
            this.reasonOptions = [];
            this.reasonsForm.reset();
          }
        });
        // Set a timeout to automatically trigger the reject action after 10 seconds
        this.rejectTimeout = setTimeout(() => {
          // this.loggerService.debug('Audit Trail: Timeout reached, rejecting the dialog');
          this.confirmationService.close(); // Close the dialog
        }, (this.localStorageService.getSessionSettings()?.timeoutGeneral ?? 10) * 1000); // 10,000 ms = 10 seconds
      }
    }
    else {
      this.lastReason = 'REASONS_NOT_CONFIGURED';
      attributes.push({ name: AuditTrailFields.REASON, value: 'REASONS_NOT_CONFIGURED' });
      if (register)
        this.registerAuditTrailAction(executor, actionId, 0, 'SUCCESS', '', attributes);
      _callback();
    }
  }

  clearRejectTimeout() {
    // this.loggerService.debug('Audit Trail: Clearing reject timeout');
    if (this.rejectTimeout) {
      clearTimeout(this.rejectTimeout);
    }
    this.rejectTimeout = null;
  }

  buildNewActionData(
    numId: string,
    actionId: string,
    actionDuration: number,
    actionResult: string,
    technologyId: string,
    attributes: ExtraData[],
    konektorProperties: KonektorPropertiesModel,
  ): NewAction {
    let newAction = new NewAction();

    const reportedAt = new Timestamp();
    const now = new Date();

    reportedAt.setSeconds(Math.floor(now.getTime() / 1000));
    reportedAt.setNanos((now.getTime() % 1000) * 1e6);
    newAction.setReportedAt(reportedAt);

    const dataStruct = new Struct();
    var dataFieldsMap = dataStruct.getFieldsMap();

    dataFieldsMap.set(AuditTrailFields.APPLICATION_ID, new Value().setStringValue(capitalizeFirstLetter(environment.application.toLowerCase())));
    dataFieldsMap.set(AuditTrailFields.APP_VERSION, new Value().setStringValue(environment.version));
    dataFieldsMap.set(AuditTrailFields.ACTION_NAME, new Value().setStringValue(actionId));

    const commonAttributes = new Struct();

    var commonAttributesMap = commonAttributes.getFieldsMap();

    var roles: string[] = [];

    let executorId = '';
    if (numId == 'NO_SUBJECT_ID') {
      executorId = konektorProperties.locationId + ' ' + konektorProperties.segmentId + ' ' + konektorProperties.deviceId
    }
    else {
      executorId = numId;
      roles = this.localStorageService.getUser()?.roles?.map((role: RoleEntity) => role.name);
    }

    commonAttributesMap.set(AuditTrailFields.EXECUTOR_ID, new Value().setStringValue(executorId));
    commonAttributesMap.set(AuditTrailFields.EXECUTOR_PROFILE, new Value().setStringValue(roles?.join(',')));
    commonAttributesMap.set(AuditTrailFields.LOCATION_ID, new Value().setStringValue(konektorProperties.locationId ?? ""));
    commonAttributesMap.set(AuditTrailFields.SEGMENT_ID, new Value().setStringValue(konektorProperties.segmentId ?? ""));
    commonAttributesMap.set(AuditTrailFields.DEVICE_ID, new Value().setStringValue(konektorProperties.deviceId ?? ""));
    commonAttributesMap.set(AuditTrailFields.ACTION_RESULT, new Value().setStringValue(actionResult));
    commonAttributesMap.set(AuditTrailFields.TECHNOLOGY_ID, new Value().setStringValue(technologyId != '' ? getTechId(technologyId) : technologyId));

    dataFieldsMap.set(AuditTrailFields.COMMON_GROUP, new Value().setStructValue(commonAttributes));

    const timingAttributes = new Struct();
    var timingAttributesMap = timingAttributes.getFieldsMap();
    timingAttributesMap.set(AuditTrailFields.TOTAL_TIME, new Value().setNumberValue(Math.trunc(actionDuration)));

    dataFieldsMap.set(AuditTrailFields.TIMING_GROUP, new Value().setStructValue(timingAttributes));

    const extraAttributes = new Struct();
    var extraAttributesMap = extraAttributes.getFieldsMap();

    attributes.forEach((attr) => {
      extraAttributesMap.set(attr.name!, new Value().setStringValue(attr.value!));
    });

    dataFieldsMap.set(AuditTrailFields.EXTRA_GROUP, new Value().setStructValue(extraAttributes));

    newAction.setData(dataStruct);

    // this.loggerService.debug(newAction.toObject());

    return newAction;
  }
}