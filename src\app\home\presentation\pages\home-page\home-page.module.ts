import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { HomePageRoutingModule } from './home-page-routing.module';
import { HomePageComponent } from './home-page/home-page.component';
import { TranslateModule } from '@ngx-translate/core';
import { ToastModule } from 'primeng/toast';
import { DashboardModule } from 'src/verazial-common-frontend/modules/shared/components/dashboard/dashboard/dashboard.module';


@NgModule({
  declarations: [
    HomePageComponent
  ],
  imports: [
    /* Angular */
    CommonModule,
    /* Routing */
    HomePageRoutingModule,
    /* Translate */
    TranslateModule,
    /* PrimeNG */
    ToastModule,
    /* Custom */
    DashboardModule,
  ],
  exports: [
    HomePageComponent
  ]
})
export class HomePageModule { }
