import { Component, OnInit } from '@angular/core';
import { DashboardItemStructure } from 'src/verazial-common-frontend/modules/shared/components/dashboard/dashboard-item/dashboard-item/dashboard-item.component';

@Component({
  selector: 'app-home-page',
  templateUrl: './home-page.component.html',
  styleUrl: './home-page.component.css'
})
export class HomePageComponent implements OnInit {

  dashboardData: DashboardItemStructure = new DashboardItemStructure();

  constructor() { }

  ngOnInit() {
    this.dashboardData = {
      serverDashboard: {
          serverDashboard: true,
          hardDrive: true,
          ram: true,
          biometricServerStatus: true,
          applicationsStatus: false,
      },
      administrationDashboard: {
          administrationDashboard: true,
          totalSubjects: true,
          totalUsers: true,
          totalIrisSamples: true,
          totalFingerprintSamples: true,
          totalFacialSamples: true,
          totalRolledFingerprintSamples: true,
          totalPalmSamples: true,
      },
      licensesDashboard: {
          licensesDashboard: false,
          totalLicenses: false,
          totalAvailableLicenses: false,
          totalAssignedLicenses: false,
          usedLicensesBreakdown: false,
      }
    };
  }
}
