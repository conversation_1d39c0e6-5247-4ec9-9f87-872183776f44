import { ArrayOfRoles, GetSubjectsRequestGrpcModel } from "src/verazial-common-frontend/core/generated/subject/subject_pb";
import { RoleEntity } from "src/verazial-common-frontend/core/general/common/entity/role.entity";
import { RoleMapper } from "src/verazial-common-frontend/core/general/role/data/mapper/role.mapper";
import { FilterConditionType, FilterEntity } from "src/verazial-common-frontend/core/models/filter.entity";
import { dateToTimestamp } from "src/verazial-common-frontend/core/util/date-to-timestamp";
import { toEnum } from "src/verazial-common-frontend/core/util/to-enum";

export function toArrayOfRoles(listOfRoles?: RoleEntity[]): ArrayOfRoles {
    let roleMapper = new RoleMapper();
    let arrayOfRoles = new ArrayOfRoles();

    if (!listOfRoles) return arrayOfRoles;

    listOfRoles?.forEach(role => {
        arrayOfRoles.addRoles(roleMapper.mapTo(role))
    })
    return arrayOfRoles;
}

export function toListOfRoles(arrayOfRoles?: ArrayOfRoles): RoleEntity[] {
    let roleMapper = new RoleMapper();
    let listOfRoles: RoleEntity[] = [];

    arrayOfRoles?.getRolesList().forEach(role => {
        listOfRoles.push(roleMapper.mapFrom(role))
    })

    return listOfRoles;
}

export function listOfFilterEntityToArrayOfFilterGrpcModel(listOfFilters?: FilterEntity[]): GetSubjectsRequestGrpcModel.ArrayOfFilters {
    let arrayOfFilters = new GetSubjectsRequestGrpcModel.ArrayOfFilters();
    if (listOfFilters && listOfFilters.length === 0) return arrayOfFilters;
    listOfFilters?.forEach(filter => {
        let filterGrpcModel = new GetSubjectsRequestGrpcModel.Filter();
        switch (filter.condition?.type) {
            case FilterConditionType.CONTAINS:
                let fieldContains = new GetSubjectsRequestGrpcModel.Filter.FieldContains();
                fieldContains.setField(filter?.condition?.field!);
                fieldContains.setValue(filter?.condition?.value!);
                filterGrpcModel.setFieldContains(fieldContains);
                break;
            case FilterConditionType.EQUALS:
                let fieldEquals = new GetSubjectsRequestGrpcModel.Filter.FieldEquals();
                fieldEquals.setField(filter?.condition?.field!);
                fieldEquals.setValue(filter?.condition?.value!);
                filterGrpcModel.setFieldEquals(fieldEquals);
                break;
            case FilterConditionType.BETWEEN:
                let fieldBetween = new GetSubjectsRequestGrpcModel.Filter.FieldDateRange();
                fieldBetween.setField(filter?.condition?.field!);
                fieldBetween.setFrom(dateToTimestamp(filter?.condition?.from!));
                fieldBetween.setTo(dateToTimestamp(filter?.condition?.to!));
                filterGrpcModel.setFieldDateRange(fieldBetween);
                break;
            case FilterConditionType.CUSTOM:
                let fieldCustom = new GetSubjectsRequestGrpcModel.Filter.FieldCustom();
                fieldCustom.setField(filter?.condition?.field!);
                fieldCustom.setValue(filter?.condition?.value!);
                fieldCustom.setType(toEnum(GetSubjectsRequestGrpcModel.CustomFilterType, filter?.condition?.customType!)!);
                filterGrpcModel.setFieldCustom(fieldCustom);
                break
        }
        arrayOfFilters.addFilters(filterGrpcModel);
    })
    return arrayOfFilters;
}