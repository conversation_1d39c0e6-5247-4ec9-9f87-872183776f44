<div class="center-screen">
    <!-- <app-camera-dialog
        [canReadAndWrite]="readAndWritePermissions"
        [userSubject]="userSubject"
        [selectedCurrentResult]="selectedCurrentResult"
        [imagePlaceholder]="imagePlaceholder"
        [showCameraDialog]="showProfilePictureDialog"
        [replaceImage]="true" [cropImage]="true"
        (result)="onCameraResult($event)"></app-camera-dialog> -->
    <app-camera
        [canReadAndWrite]="readAndWritePermissions"
        [selectedCurrentResult]="selectedCurrentResult"
        [showCameraDialog]="showProfilePictureDialog"
        [imagePlaceholder]="imagePlaceholder"
        [aspectRatio]="1"
        (result)="onCameraResult($event)"
    ></app-camera>
</div>

<!-- Upload New -->
<div class="center-screen">
    <app-upload-files
        [readAndWritePermissions]="readAndWritePermissions"
        [showUploadDialog]="showUploadDialog"
        [showForm]="false"
        [acceptedFiles]="acceptedFiles"
        [maxFileSize]="maxFileSize"
        (onCancel)="onCancelUpload()"
        (onUpload)="onUploadResult($event)"
    ></app-upload-files>
</div>

<p-dialog header="{{ 'content.selectOption' | translate }}" [modal]="true" [closable]="false" [(visible)]="showSelectOptionDialog">
    <ng-template pTemplate="content">
        <app-new-select-option (onOption1)="openCamera()" (onOption2)="openFileUpload()" (onCancel)="showSelectOptionDialog = false"></app-new-select-option>
    </ng-template>
</p-dialog>

<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>

<div class="userSubjectEditCreateForm p-3" [formGroup]="form">
    <div class="pr-5">
        <app-profile-pic [image]="userSubjectData?.pic ? ((userSubjectData?.pic.includes('data:image/jpeg;base64,') ? '' : (this.userSubjectData?.pic?.includes(imagePlaceholder) ? '' : 'data:image/jpeg;base64,')) + this.userSubjectData?.pic) : imagePlaceholder"></app-profile-pic>
        <div *ngIf="readAndWritePermissions && (userIsVerified && isVerified)" class="create-profile-pic-button-div">
            <p-button
                *ngIf="(userSubjectData?.pic?userSubjectData?.pic:imagePlaceholder) == imagePlaceholder else options"
                [disabled]="!readAndWritePermissions || (actionType == userSubjectActionTypes.UPDATE && !userIsVerified)"
                icon="pi pi-plus" [rounded]="true" size="small"
                [style]="{'background': '#009BA9', 'border-color': '#009BA9'}"
                (click)="openProfilePictureDialog()"></p-button>
            <ng-template #options>
                <p-speedDial
                    [model]="profilePicOptions" direction="down"
                    [disabled]="!readAndWritePermissions || (actionType == userSubjectActionTypes.UPDATE && !userIsVerified)"
                />
            </ng-template>
        </div>
    </div>
    <p-scrollPanel [style]="{ maxWidth: '65vw', maxHeight: '90vh' }">
        <div class="grid form-fields">
            <div class="lg:col-12 sm:col-12 width100" style="padding-bottom: 0rem;">
                <div class="flex justify-content-end requiredFieldsLabel">
                    {{ 'content.requiredFields' | translate }} <span class="requiredStar">*</span>
                </div>
                <div class="pb-1">
                    <label class="label-form" for="numId">{{ 'table.num_id' | translate }} <span
                            *ngIf="isRequiredField('numId')" class="requiredStar">*</span></label>
                    <input type="text" pInputText formControlName="numId" (ngModelChange)="trackDataChange()"
                        [class.ng-dirty]="!isValid('numId') && form.controls['numId'].touched" />
                    <small *ngIf="!isValid('numId') && form.controls['numId'].touched" [style]="{'color': 'red'}">
                        {{ 'messages.error_isRequiredField' | translate }}
                    </small>
                </div>
            </div>
            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                <div class="pb-1">
                    <label class="label-form" for="names">{{ 'table.names' | translate }} <span
                            *ngIf="isRequiredField('names')" class="requiredStar">*</span></label>
                    <input type="text" pInputText formControlName="names" (ngModelChange)="trackDataChange()"
                        [class.ng-dirty]="!isValid('names') && form.controls['names'].touched" />
                    <small *ngIf="!isValid('names') && form.controls['names'].touched" [style]="{'color': 'red'}">{{
                        'messages.error_isRequiredField' | translate }}</small>
                </div>
            </div>
            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                <div class="pb-1">
                    <label class="label-form" for="lastNames">{{ 'table.lastNames' | translate }} <span
                            *ngIf="isRequiredField('lastNames')" class="requiredStar">*</span></label>
                    <input type="text" pInputText formControlName="lastNames" (ngModelChange)="trackDataChange()"
                        [class.ng-dirty]="!isValid('lastNames') && form.controls['lastNames'].touched" />
                    <small *ngIf="!isValid('lastNames') && form.controls['lastNames'].touched" [style]="{'color': 'red'}">{{
                        'messages.error_isRequiredField' | translate }}</small>
                </div>
            </div>
            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                <div class="pb-1">
                    <label class="label-form" for="emai">{{ 'content.email' | translate }} <span
                            *ngIf="isRequiredField('email')" class="requiredStar">*</span></label>
                    <input type="text" pInputText formControlName="email" (ngModelChange)="trackDataChange()"
                        [class.ng-dirty]="!isValid('email') && form.controls['email'].touched" />
                    @if(!isValid('email') && form.controls['email'].touched){
                        <div class="flex flex-column">
                            <small *ngIf="checkSpecificError('email', 'email')" class="mt-1" [style]="{'color': 'red'}">{{ 'messages.error_must_be_valid_email' | translate }}</small>
                            <small *ngIf="checkSpecificError('email', 'required')" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                    }
                </div>
            </div>
            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                <div class="pb-1">
                    <label class="label-form" for="birthdate">{{ 'table.birthdate' | translate }} <span
                            *ngIf="isRequiredField('birthdate')" class="requiredStar">*</span></label>
                    <p-calendar appendTo="body" formControlName="birthdate" (ngModelChange)="trackDataChange()" [iconDisplay]="'input'"
                        [showIcon]="true" [maxDate]="maxDate" inputId="icondisplay" dateFormat="{{ 'dateFormat' | translate }}" />
                    <small *ngIf="!isValid('birthdate') && form.controls['birthdate'].touched" [style]="{'color': 'red'}">{{
                        'messages.error_isRequiredField' | translate }}</small>
                </div>
            </div>
            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                <div class="pb-1">
                    <label class="label-form" for="gender">{{ 'table.gender' | translate }} <span
                            *ngIf="isRequiredField('gender')" class="requiredStar">*</span></label>
                    <p-dropdown formControlName="gender" [(ngModel)]="userSubjectGender" (ngModelChange)="trackDataChange()"
                        appendTo="body" [options]="genderOptions" optionLabel="value" placeholder="{{ 'content.select' | translate }}" />
                </div>
            </div>
            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                <div class="pb-1">
                    <label class="label-form" for="language">{{ 'table.language' | translate }} <span
                            *ngIf="isRequiredField('language')" class="requiredStar">*</span></label>
                    <p-dropdown formControlName="language" [(ngModel)]="userSubjectLanguage" (ngModelChange)="trackDataChange()"
                        appendTo="body" [options]="languageOptions" optionLabel="value" placeholder="{{ 'content.select' | translate }}" />
                </div>
            </div>
            <div *ngIf="dataSourceOptions.length > 0" class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                <div class="pb-1">
                    <label class="label-form" for="datasource">{{ 'table.datasource' | translate }} <span
                            *ngIf="isRequiredField('datasource')" class="requiredStar">*</span></label>
                    <p-dropdown formControlName="datasource" [(ngModel)]="userSubjectDataSource" (ngModelChange)="trackDataChange()"
                        appendTo="body" [showClear]="true" [options]="dataSourceOptions" optionLabel="value" placeholder="{{ 'content.select' | translate }}" />
                </div>
            </div>
            <div class="col-4">
                    <p-divider />
                </div>
                <div class="col-4 flex justify-content-center align-items-center">
                    <label class="label-form">{{ (isUser ? 'table.user_roles' : 'table.subject_profiles') | translate }} </label>
                </div>
                <div class="col-4">
                    <p-divider />
                </div>
            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                <div class="pb-1">
                    <label class="label-form" for="roles">
                        {{ (isUser ? 'table.roles' : 'table.profiles') | translate }}
                        <span *ngIf="isRequiredField('roles')" class="requiredStar">*</span>
                    </label>
                    <p-multiSelect maxSelectedLabels="1" [options]="roles" formControlName="roles"
                        appendTo="body" (ngModelChange)="updatePrincipalRoles()" optionLabel="name"
                        placeholder="{{ 'content.select' | translate }}" />
                </div>
            </div>
            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                <div class="pb-1">
                    <label class="label-form" for="rol">
                        {{ (isUser ? 'table.main_role' : 'table.main_profile') | translate }}
                        <span *ngIf="isRequiredField('rol')" class="requiredStar">*</span>
                    </label>
                    <p-dropdown [options]="filteredRoles" formControlName="rol" appendTo="body"
                        (onChange)="trackDataChange()" optionLabel="name"
                        placeholder="{{ 'content.select' | translate }}">
                    </p-dropdown>
                </div>
            </div>
            @if(isUser){
                @if(actionType ==userSubjectActionTypes.CREATE){
                    <div class="col-4">
                        <p-divider />
                    </div>
                    <div class="col-4 flex justify-content-center align-items-center">
                        <label class="label-form">{{ 'table.subject_profiles' | translate }} </label>
                    </div>
                    <div class="col-4">
                        <p-divider />
                    </div>
                    <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                        <div class="pb-1">
                            <label class="label-form" for="subjectProfiles">
                                {{ 'table.profiles' | translate }}
                                <span *ngIf="isRequiredField('subjectProfiles')" class="requiredStar">*</span>
                            </label>
                            <p-multiSelect maxSelectedLabels="1" [options]="profiles" formControlName="subjectProfiles"
                                appendTo="body" (ngModelChange)="updatePrincipalProfiles()" optionLabel="name"
                                placeholder="{{ 'content.select' | translate }}" />
                        </div>
                    </div>
                    <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                        <div class="pb-1">
                            <label class="label-form" for="mainProfile">
                                {{ 'table.main_profile' | translate }}
                                <span *ngIf="isRequiredField('mainProfile')" class="requiredStar">*</span>
                            </label>
                            <p-dropdown [options]="filteredProfiles" formControlName="mainProfile" appendTo="body"
                                (onChange)="trackDataChange()" optionLabel="name"
                                placeholder="{{ 'content.select' | translate }}">
                            </p-dropdown>
                        </div>
                    </div>
                }
                @if(isSelf){
                    <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                        <div class="pb-1">
                            <label class="label-form" for="password">{{ (actionType == userSubjectActionTypes.CREATE ? 'content.password' : 'content.new_password') | translate }} <span
                                    *ngIf="isRequiredField('password')" class="requiredStar">*</span></label>
                            <p-password formControlName="password" [toggleMask]="true" (ngModelChange)="trackDataChange()"
                                (copy)="disableCopyPaste($event)" (cut)="disableCopyPaste($event)"
                                (paste)="disableCopyPaste($event)" appendTo="body"
                                [class.ng-dirty]="!isValid('password') && form.controls['password'].touched" />
                                @if(!isValid('password') && form.controls['password'].touched){
                                    <div class="flex flex-column">
                                        <small *ngIf="checkSpecificError('password', 'minLength')" class="mt-1" [style]="{'color': 'red'}">{{ ('messages.invalidPasswordLength' | translate) + ' ' + minLenght }}</small>
                                        <small *ngIf="checkSpecificError('password', 'pattern')" class="mt-1" [style]="{'color': 'red'}">{{ 'messages.invalidPasswordComplexity' | translate }}</small>
                                        <small *ngIf="checkSpecificError('password', 'required')" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                    </div>
                                }
                        </div>
                    </div>
                    <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                        <div class="pb-1">
                            <label class="label-form" for="repeatPassword">{{ 'content.repeat_password' | translate }} <span
                                    *ngIf="isRequiredField('repeatPassword')" class="requiredStar">*</span></label>
                            <p-password formControlName="repeatPassword" [toggleMask]="true" (ngModelChange)="trackDataChange()"
                                (copy)="disableCopyPaste($event)" (cut)="disableCopyPaste($event)"
                                (paste)="disableCopyPaste($event)" appendTo="body"
                                [class.ng-dirty]="!isValid('repeatPassword') && form.controls['repeatPassword'].touched" />
                            @if(passwordNotMatch){
                                <small class="mt-2" [style]="{'color': 'red'}">{{ 'messages.password_not_match' | translate }}</small>
                            }
                        </div>
                    </div>
                }
                @if(actionType != userSubjectActionTypes.CREATE){
                    <div class="lg:col-6 sm:col-12 width100 last-field flex mt-2" style="padding-top: 0rem;">
                        <div class="flex flex-row gap-3 justify-content-center align-items-center align-content-center">
                            <p-checkbox formControlName="mustUpdate" [binary]="true" (ngModelChange)="trackDataChange()"
                                inputId="mustUpdate" />
                            <label for="mustUpdate">{{'content.must_update' | translate}}</label>
                        </div>
                    </div>
                }
            }
        </div>
    </p-scrollPanel>
</div>