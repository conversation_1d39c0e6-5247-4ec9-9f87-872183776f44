<!-- Toast and Confirmation Dialog -->
<p-toast></p-toast>
<p-confirmDialog [style]="{width: '395px'}"></p-confirmDialog>

<!-- Inactivity Monitor Component -->
<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>

<!-- Your component content -->
<div class="example-component">
  <div class="header">
    <h2>Example Component with Audit Trail and Inactivity Monitor</h2>
    <p>This example shows how to integrate InactivityMonitorComponent with AuditTrailService</p>
  </div>
  
  <div class="actions-section">
    <h3>Actions that trigger Audit Trail with Inactivity Monitoring</h3>
    
    <div class="button-group">
      <button 
        pButton 
        type="button" 
        label="Delete Item" 
        icon="pi pi-trash"
        class="p-button-danger"
        (click)="deleteItem()">
      </button>
      
      <button 
        pButton 
        type="button" 
        label="Update Item" 
        icon="pi pi-pencil"
        class="p-button-warning"
        (click)="updateItem()">
      </button>
      
      <button 
        pButton 
        type="button" 
        label="Create Item" 
        icon="pi pi-plus"
        class="p-button-success"
        (click)="createItem()">
      </button>
    </div>
  </div>

  <div class="info-section">
    <h3>How it works:</h3>
    <ul>
      <li>When you click any action button, the AuditTrailService will show a reason selection dialog</li>
      <li>The InactivityMonitorComponent will start monitoring for user inactivity during the dialog</li>
      <li>If the user is inactive for the configured timeout period, the dialog will automatically close</li>
      <li>The inactivity timeout is configured from session settings (timeoutMessages)</li>
      <li>User activity (mouse movement, clicks, keyboard input) resets the inactivity timer</li>
    </ul>
  </div>

  <div class="status-section">
    <h3>Current Status:</h3>
    <p><strong>Inactivity Timeout Limit:</strong> {{ auditTrailService.confirmDialogTimeoutLimit }}ms</p>
    <p><strong>Monitoring Active:</strong> {{ auditTrailService.startCheckingInactivity ? 'Yes' : 'No' }}</p>
    <p><strong>Last Reason Selected:</strong> {{ auditTrailService.lastReason || 'None' }}</p>
  </div>
</div>
