import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ListApplicationFlowsComponent } from './list-application-flows/list-application-flows.component';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { MessagesModule } from 'primeng/messages';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { TooltipModule } from 'primeng/tooltip';
import { InactivityMonitorModule } from 'src/verazial-common-frontend/modules/shared/components/inactivity-monitor/inactivity-monitor.module';



@NgModule({
  declarations: [
    ListApplicationFlowsComponent
  ],
  imports: [
    /* Angular */
    CommonModule,
    /* Translate */
    TranslateModule,
    /* Forms */
    FormsModule,
    ReactiveFormsModule,
    /* PrimeNG */
    TableModule,
    ButtonModule,
    TagModule,
    ConfirmDialogModule,
    MessagesModule,
    ToastModule,
    DropdownModule,
    DialogModule,
    InputSwitchModule,
    InputTextModule,
    IconFieldModule,
    InputIconModule,
    TooltipModule,
    CalendarModule,
    /* Custom */
    InactivityMonitorModule,
  ],
  exports:[
    ListApplicationFlowsComponent
  ]
})
export class ListApplicationFlowsModule { }
