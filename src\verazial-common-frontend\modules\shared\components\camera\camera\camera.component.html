<p-confirmDialog/>
<p-toast/>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<p-dialog [(visible)]="showCameraDialog" [modal]="true" [draggable]="false" [resizable]="false"
    [style]="{background: '#FFFFFF', borderRadius: '10px', 'min-width': '370px', 'min-height': '375px', 'max-height': '75vh'}"
    [styleClass]="'flex justify-content-center'"
>
    <ng-template pTemplate="headless">
        <div class="close-button-wrap" [style]="{left: closeDialogLeft}">
            <p-button pRipple severity="secondary" class="m-1" icon="pi pi-times" [rounded]="true"
                (click)="closeDialog()"></p-button>
        </div>
        <div class="flex flex-column justify-content-center h-full w-full p-3">
            <div *ngIf="cameraError" class="flex flex-column align-items-center justify-content-center p-4">
                <i class="pi pi-exclamation-triangle" style="font-size: 3.5rem"></i>
                <div class="text-2xl text-center title font-semibold my-2">{{ 'titles.CameraError' | translate }}</div>
                <div class="">{{ 'messages.ContactAdminCamera' | translate }}</div>
            </div>
            <div *ngIf="showNoData" class="flex justify-content-center">
                {{ showNoDataMessage | translate }}
            </div>
            <div>
                <div [ngStyle]="{'display' : (captured || showNoData || cameraError ? 'none' : 'block'), 'aspect-ratio': aspectRatio}" class="video-container">
                    <div *ngIf="!cameraCapturing && !showNoData && !cameraError" class="cameraDialog flex align-items-center justify-content-center">
                        <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" ariaLabel="loading" />
                    </div>
                    <video class="streamContainer" #videoElement autoplay [ngStyle]="{'display' : (managerSettings?.camerasFromKonektor || !cameraCapturing ? 'none' : 'block')}"></video>
                    <img class="streamContainer" #imageElement [src]="'data:image/jpeg;base64,' + konektorStream" [ngStyle]="{'display' : (!managerSettings?.camerasFromKonektor || !cameraCapturing ? 'none' : 'block')}">
                </div>
                <canvas #canvasElement style="display: none;"></canvas>
            </div>
            <div class="flex justify-content-center {{ captured ? 'pt-3' : ''}}">
                <img #capturedImage class="capturedImage" [src]="capturedImageSrc"
                    [ngStyle]="{'display': captured ? 'block' : 'none'}" />
            </div>
            <div *ngIf="!showNoData && !cameraError" class="flex flex-row align-items-center justify-content-center pt-3">
                <div *ngIf="canReadAndWrite && showCameraDropdown && !captured" [formGroup]="cameraForm" class="flex align-items-center">
                    <p-dropdown
                        [style]="{'max-width': '200px'}"
                        appendTo="body"
                        [options]="cameras"
                        formControlName="camera"
                        (onChange)="changeCamera()"
                    />
                </div>
                <div class="flex justify-content-center align-items-center">
                    <p-button
                        *ngIf="canReadAndWrite && captured && isNew"
                        label="{{ 'content.changePhoto' | translate }}"
                        [style]="{'background': '#204887', 'border-color': '#204887', 'color': '#FFFFFF'}"
                        class="m-1"
                        [rounded]="true"
                        icon="pi pi-camera"
                        iconPos="right"
                        (click)="startCamera()"
                    />
                    <p-button
                        *ngIf="canReadAndWrite && captured && isNew"
                        label="{{ 'save' | translate }}"
                        [style]="{'background': '#009BA9', 'border-color': '#009BA9', 'color': '#FFFFFF'}"
                        class="m-1"
                        [rounded]="true"
                        icon="pi pi-save"
                        iconPos="right"
                        (click)="submitPhoto()"
                    />
                    <p-button *ngIf="canReadAndWrite && captured && !isNew"
                        label="{{ 'delete' | translate }}"
                        severity="danger"
                        class="m-1"
                        [rounded]="true"
                        icon="pi pi-trash"
                        iconPos="right"
                        (click)="confirmDelete()"></p-button>
                    <p-button
                        *ngIf="canReadAndWrite && !captured"
                        label="{{ 'content.takePhoto' | translate }}"
                        [style]="{'background': '#204887', 'border-color': '#204887', 'color': '#FFFFFF'}"
                        class="m-1"
                        [rounded]="true"
                        icon="pi pi-camera"
                        iconPos="right"
                        [disabled]="!cameraCapturing"
                        (click)="captureImage()"
                    />
                </div>
            </div>
        </div>
    </ng-template>
</p-dialog>