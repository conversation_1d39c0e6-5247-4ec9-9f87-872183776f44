<p-confirmDialog />
<p-toast/>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<!-- New Picture Dialog -->
<p-dialog [(visible)]="showCameraDialog" [modal]="true" [draggable]="false" [resizable]="false"
    [style]="{background: '#FFFFFF', borderRadius: '10px'}">
    <ng-template pTemplate="headless">
        <!-- Cancel -->
        <div class="close-button-wrap" [style]="{left: closeDialogLeft}">
            <p-button pRipple
                severity="secondary"class="m-1"
                icon="pi pi-times" [rounded]="true" (click)="closeDialog()"></p-button>
        </div>
        <div class="flex flex-column align-items-center justify-content-center cameraDialog">
            <!-- Content -->
            <img *ngIf="!capturing" #cameraImg
            [src]="( selectedCurrentResult.content.includes('assets/') ? '' : 'data:image/jpeg;base64,') + selectedCurrentResult.content"
            [alt]="selectedCurrentResult.name + '-' + selectedCurrentResult.number"
            class="{{ cropImage ? 'image capturingCrop' : 'imageShowing showing' }}"
            [style]="{'opacity': cropImage ? 0 : 1}"
            [ngStyle]="{'background': '#DEE2E6', 'border-radius': '10px', 'z-index': 1 }"
            >
            <video #videoElement class="p-3 image" autoplay playsinline [ngStyle]="{'display' : captured ? 'none' : 'flex'}"></video>
            <canvas #canvasElement style="display: none;"></canvas>
            <img #capturedImage class="image" />
            <!-- Options -->
            <div class="flex justify-content-center">
                <!-- Available Cameras -->
                <div *ngIf="canReadAndWrite && !captured && (selectedCurrentResult.id == undefined || replaceImage) && showCameraDropdown" [formGroup]="cameraForm" class="flex align-items-center">
                    <p-dropdown
                        appendTo="body"
                        [options]="cameras"
                        formControlName="camera"
                        (onChange)="startVideo()"
                    />
                </div>
                <!-- Delete -->
                <p-button *ngIf="canReadAndWrite && !captured && capturing else remove" pRipple
                    [style]="{'background': '#009BA9', 'border-color': '#009BA9'}" class="m-1"
                    icon="pi pi-camera" [rounded]="true" (click)="onSubmitAdd()"></p-button>
                <ng-template #remove>
                    <p-button *ngIf="canReadAndWrite && selectedCurrentResult.id != undefined && !replaceImage" pRipple
                        severity="danger" class="m-1"
                        icon="pi pi-trash" [rounded]="true" (click)="confirmDelete()"></p-button>
                </ng-template>
                <!-- Create -->
                 <div *ngIf="canReadAndWrite && captured && !capturing" class="flex">
                     <p-button pRipple
                         [style]="{'background': 'rgb(32, 72, 135)', 'border-color': 'rgb(32, 72, 135)'}" class="m-1"
                         label="{{'content.replace_image' | translate}}" icon="pi pi-refresh" (click)="onSubmitAdd()"></p-button>
                     <p-button pRipple
                         [style]="{'background': '#009BA9', 'border-color': '#009BA9'}" class="m-1"
                         label="{{'save' | translate}}" (click)="onSubmitCreate()"></p-button>
                 </div>
            </div>
        </div>
    </ng-template>
</p-dialog>

<ng-template #loadingSpinner>
    <div class="cameraDialog flex align-items-center justify-content-center">
        <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" ariaLabel="loading" />
    </div>
</ng-template>

<ng-template #noData>
    <div *ngIf="!capturing && selectedCurrentResult.content === imagePlaceholder" class="flex justify-content-center m-5">
        {{ 'content.noDataAvailable' | translate }}
    </div>
</ng-template>