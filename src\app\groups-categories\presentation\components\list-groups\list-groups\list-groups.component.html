<p-toast></p-toast>
<p-confirmDialog [style]="{width: '395px'}"></p-confirmDialog>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<div class="container-flow">
    <div *ngIf="groups.length != 0 else empty" class="content-list-flows gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <label class="title-list-flows">{{ "titles.categories" | translate}}</label>
            <div class="flex flex-row flex-wrap justify-content-center gap-4 align-items-center">
                <p-iconField iconPosition="right">
                    <input pInputText type="text"
                        [(ngModel)]="searchValue"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="{{ 'content.search' | translate }}"
                    />
                    <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                </p-iconField>
                <div class="add-action-main-full">
                    <p-button
                        [disabled]="!readAndWritePermissions"
                        [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'category.new_category'| translate }}"  icon="pi pi-plus" iconPos="right" [rounded]="true"
                        (onClick)="createNewGroup()"></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button
                        [disabled]="!readAndWritePermissions"
                        [style]="{'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        icon="pi pi-plus" [rounded]="true"
                        (onClick)="createNewGroup()"></p-button>
                </div>
            </div>
        </div>
        <div>

        </div>
        <p-table
            #dt
            [value]="groups"
            (onFilter)="onFilter($event, dt)"
            dataKey="id"
            [rowHover]="true"
            [paginator]="true"
            [rows]="10"
            [rowsPerPageOptions]="[5, 10, 20]"
            [scrollable]="true"
            scrollHeight="flex"
            scrollDirection="horizontal"
            [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="['name', 'type', 'createdAt', 'updatedAt']"
            [sortField]="'name'" [sortOrder]="1">
            <ng-template pTemplate="header">
                <tr>
                    <th class="fixed-column-sm" pSortableColumn="name">{{'content.name' | translate}}<p-sortIcon field="name"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="type">{{ 'content.type' | translate }}<p-sortIcon field="type"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="createdAt">{{ 'created_at' | translate }}<p-sortIcon field="createdAt"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="updatedAt">{{ 'updated_at' | translate }}<p-sortIcon field="updatedAt"></p-sortIcon></th>
                    <th style="width: 5%;" alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th>
                        <p-columnFilter type="text" field="name" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="type" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [ngModel]="value"
                                    [options]="[
                                        {label: 'content.subject', value: '0'},
                                        {label: 'content.location', value: '1'},
                                        {label: 'content.schedule', value: '2'},
                                    ]"
                                    (onChange)="filter($event.value);"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="label"
                                    optionValue="value"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ (value == '0' ? 'content.subject' : value == '1' ? 'content.location' : 'content.schedule') | translate }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        <p-tag severity="info" value="{{option.label | translate }}" icon="pi pi-{{ getIcon(option.value) }}" [style]="{'color': getColour(option.value), 'background': getBackgroundColour(option.value)}"></p-tag>
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="createdAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroup">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'createdAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="date" field="updatedAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroupDate">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'updatedAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-data>
                <tr [pSelectableRow]="data">
                    <td (click)="editGroup(data)" showDelay="1000" pTooltip="{{data.name}}" tooltipPosition="top" class="ellipsis-cell">{{ data.name }}</td>
                    <td (click)="editGroup(data)" showDelay="1000" pTooltip="{{getCategoryTranslation(data.type)}}" tooltipPosition="top" class="ellipsis-cell"><p-tag severity="info" [value]="getCategoryTranslation(data.type)" icon="pi pi-{{ getIcon(data.type) }}" [style]="{'color': getColour(data.type), 'background': getBackgroundColour(data.type)}"></p-tag></td>
                    <td (click)="editGroup(data)" showDelay="1000" pTooltip="{{data.createdAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ data.createdAt | date:('dateTimeFormat' | translate) }}</td>
                    <td (click)="editGroup(data)" showDelay="1000" pTooltip="{{data.updatedAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ data.updatedAt | date:('dateTimeFormat' | translate) }}</td>
                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                        <div class="flex flex-row">
                            <button pButton pRipple icon="pi pi-pencil" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="editGroup(data)"></button>
                            <button pButton pRipple icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="deleteGroup(data)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>

<p-dialog [(visible)]="showNewGroupDialog" styleClass="p-fluid" [modal]="true" [closable]="true">
    <ng-template pTemplate="header">
        <div></div>
        <div class="dialog-title">
            {{ groupData ? groupData.name : 'category.new_category' | translate }}
        </div>
    </ng-template>
    <ng-template pTemplate="content">
        <app-new-group [readAndWritePermissions]="readAndWritePermissions" [groupData]="groupData" (operationStatus)="operationStatus($event)" [operationType]="operationType"></app-new-group>
    </ng-template>
</p-dialog>

<ng-template #empty>
    <div class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ 'titles.categories' | translate}}</label>
                </div>
            </div>
        </div>
    </div>
    <div style="height: 65vh;" class="flex justify-content-center align-items-center">
        <app-empty
            [readAndWritePermissions]="readAndWritePermissions"
            buttonLabel="category.new_category"
            titleLabel="category.no_categories_available"
            contentHeight="300px"
            (clicked)="createNewGroup()"
        ></app-empty>
    </div>
</ng-template>