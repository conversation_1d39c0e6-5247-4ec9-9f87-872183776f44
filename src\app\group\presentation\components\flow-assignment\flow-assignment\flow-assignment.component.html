<div class="admin-groups card mt-4">
    <p-pickList 
    [source]="sourceListFlows" 
    [target]="targetListFlows" 
    sourceHeader="{{ 'content.available' | translate }}" 
    targetHeader="{{ 'content.selected' | translate }}" 
    [dragdrop]="true" 
    [showSourceControls]="false"
    [showTargetControls]="false"
    filterBy="name" 
    [responsive]="true" 
    [sourceStyle]="{ height: '20rem', width: '20rem' }"
    [targetStyle]="{ height: '20rem', width: '20rem' }"
    (onMoveToSource)="targetTrackChanges()"
    (onMoveToTarget)="targetTrackChanges()"
    (onMoveAllToSource)="targetTrackChanges()"
    (onMoveAllToTarget)="targetTrackChanges()"
    breakpoint="1400px">
        <ng-template let-flow pTemplate="item">
            <div class="flex flex-wrap p-2 align-items-center gap-3">
                <div class="flex-1 flex flex-column gap-2">
                    <span>{{ flow.name }}</span>
                </div>
            </div>
        </ng-template>
    </p-pickList>
</div>
