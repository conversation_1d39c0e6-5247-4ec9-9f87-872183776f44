import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ListDataSourcesComponent } from './list-data-sources/list-data-sources.component';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { ToastModule } from 'primeng/toast';
import { TableModule } from 'primeng/table';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DataSourcesModule } from '../data-sources/data-sources.module';
import { ListDataSourceParametersModule } from '../list-data-source-parameters/list-data-source-parameters.module';
import { DataSourceParametersModule } from '../data-source-parameters/data-source-parameters.module';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { TooltipModule } from 'primeng/tooltip';
import { EmptyModule } from 'src/verazial-common-frontend/modules/shared/components/empty/empty.module';
import { InactivityMonitorModule } from 'src/verazial-common-frontend/modules/shared/components/inactivity-monitor/inactivity-monitor.module';


@NgModule({
  declarations: [
    ListDataSourcesComponent
  ],
  imports: [
    /* Angular */
    CommonModule,
    /* Translate */
    TranslateModule,
    /* Forms */
    FormsModule,
    ReactiveFormsModule,
    /* PrimeNG */
    InputTextModule,
    ButtonModule,
    DropdownModule,
    InputTextareaModule,
    TableModule,
    ToastModule,
    DialogModule,
    ConfirmDialogModule,
    IconFieldModule,
    InputIconModule,
    TooltipModule,
    /* Custom */
    DataSourcesModule,
    ListDataSourceParametersModule,
    DataSourceParametersModule,
    EmptyModule,
    InactivityMonitorModule,
  ],
  exports: [
    ListDataSourcesComponent
  ]
})
export class ListDataSourcesModule { }
