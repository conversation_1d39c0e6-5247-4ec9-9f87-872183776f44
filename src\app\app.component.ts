import { Component, HostListener, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { AuditTrailService } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { AuthService } from 'src/verazial-common-frontend/core/services/auth-service.service';
import { EventBusService } from 'src/verazial-common-frontend/core/services/event-bus.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit{
  title = 'verazial-app';

  isLoggedIn = false;
  eventBusSub?: Subscription;

  // Interval to start checking inactivity
  intervalStart = 5000;
  // Interval to check inactivity
  interval = 1000;
  // Inactivity timeout in milliseconds
  lockTime = 0;
  // Elapsed time in milliseconds
  elapsedTime = 0;

  // Count to check if the user is active
  countRefresh = 0;
  // Flag to check if the user is active
  isActivity = false;
  // Interval to start checking inactivity
  countRefreshStart: any;
  // Interval to check if the user is inactive
  isUserActivity: any;

  managerConfig: any;

  startCheckingInactivity: boolean = false;

  constructor(
    private storageService: LocalStorageService,
    private authService: AuthService,
    private eventBusService: EventBusService,
    /*
                    ** DO NOT REMOVE **
      Audit Trail Service used for the select reason dialog
    */
    private auditTrailService: AuditTrailService,
  ) {
    this.loadManagerConfig();
  }

  ngOnInit() {}

  logout(): void {
    this.authService.logout();
  }

  loadManagerConfig() {
    setInterval(() => {
      if (!this.isEmpty(this.storageService.getUser()) && this.lockTime == 0) {
        this.managerConfig = this.storageService.getSessionSettings();
        if (this.managerConfig.timeoutInactivity > 0) {
          this.lockTime = this.managerConfig.timeoutInactivity * 1000;
          this.startCheckingInactivity = false;
          if (!this.startCheckingInactivity) {
              setTimeout(() => this.startCheckingInactivity = true, 0);
          }
        }
        else {
          this.lockTime = 0;
        }
      }
    }, this.interval)
  }

  isEmpty(obj: Object) {
    if (obj == null) {
      return true;
    }
    return Object.keys(obj).length === 0;
  }
}
