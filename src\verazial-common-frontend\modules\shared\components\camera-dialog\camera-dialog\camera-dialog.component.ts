import { After<PERSON>iewInit, Component, ElementRef, EventEmitter, HostListener, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { ConfirmationService, MessageService } from "primeng/api";
import { firstValueFrom, from, Observable } from "rxjs";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { KonektorResponseModel } from "src/verazial-common-frontend/core/general/konektor/data/model/konektor-response.model";
import { GetKonektorPropertiesUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case";
import { StopTakePhotoUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/stop-take-photo.use-case";
import { TakePhotoUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/take-photo.use-case";
import { StaticResourceEntity } from "src/verazial-common-frontend/core/general/storage/domain/entity/static-resource.entity";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { UserEntity } from "src/verazial-common-frontend/core/general/user/domain/entity/user.entity";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailService } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";

@Component({
    selector: 'app-camera-dialog',
    templateUrl: './camera-dialog.component.html',
    styleUrl: './camera-dialog.component.css',
    providers: [MessageService, ConfirmationService]
})
export class CameraDialogComponent implements OnInit, OnChanges, OnDestroy {

    /* Inputs */
    @Input() canReadAndWrite: boolean = false;
    @Input() loading: boolean = false;
    @Input() userSubject: SubjectEntity | UserEntity | undefined;
    @Input() selectedCurrentResult: StaticResourceEntity | undefined;
    @Input() imagePlaceholder: string = 'verazial-common-frontend/assets/images/admin/physicalDataPlaceholder.svg';
    @Input() showCameraDialog: boolean = false;
    @Input() replaceImage: boolean = false;
    @Input() cropImage: boolean = false;
    /* Outputs */
    @Output() result = new EventEmitter<{ action: string, staticResource: StaticResourceEntity }>();
    /* */
    @ViewChild('cameraImg') cameraImg!: ElementRef;
    @ViewChild('cameraDiv') cameraDiv!: ElementRef;
    @ViewChild('canvas') cropCanvas!: ElementRef;
    @ViewChild('videoElement') videoElement!: ElementRef<HTMLVideoElement>;
    @ViewChild('canvasElement') canvasElement!: ElementRef;
    @ViewChild('capturedImage') capturedImage!: ElementRef;
    // HostListener to listen to screen resize events
    @HostListener('window:resize', ['$event'])
    onResize(event: Event) {
        this.updateScreenSize();
    }

    /* Component States */
    capturing: boolean = false;
    captured: boolean = false;

    /* Camera */
    showCameraDropdown: boolean = false;
    availableMediaCameras: MediaDeviceInfo[] = [];
    mediaStream: MediaStream | undefined;
    cameras: string[] = [];
    cameraForm: FormGroup = new FormGroup({
        camera: new FormControl()
    });

    /* Responsive */
    screenWidth: number = 0;
    screenHeight: number = 0;

    dialogWidth: string = '350px';
    dialogHeight: string = '370px';
    closeDialogLeft: string = '375px';
    actionsTopPercent: string = '85%';

    confirmDialogTimeoutLimit: number = 0;
    startCheckingInactivity: boolean = false;

    cropRectangle = {
        x: 67,
        y: 18,
        width: 250,
        height: 250,
    }

    constructor(
        private localStorageService: LocalStorageService,
        private loggerService: ConsoleLoggerService,
        private translateService: TranslateService,
        private messageService: MessageService,
        private confirmationService: ConfirmationService,
        private auditTrailService: AuditTrailService,
        private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
        private takePhotoUseCase: TakePhotoUseCase,
        private stopTakePhotoUseCase: StopTakePhotoUseCase,
    ) {
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    }

    /* Component Functions */

    ngOnInit() {
        this.loading = true;
        this.updateScreenSize();
        this.initializeComponent();
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['showCameraDialog']) {
            if (changes['showCameraDialog'].currentValue) {
                this.loading = true;
                this.initializeComponent();
            }
            else {
                this.ngOnDestroy();
            }
        }
    }

    async ngOnDestroy() {
        this.stopVideo();
        if (this.capturing) {
            await this.stopCapture();
        }
        this.captured = false;
        // Clean up the timeout if the component is destroyed
        this.closeConfirmationDialog();
    }

    resetInactivityMonitor() {
        this.startCheckingInactivity = false;
        this.confirmDialogTimeoutLimit = 0;
    }

    async getAvailableCameras(): Promise<MediaDeviceInfo[]> {
        try {
            // Simply enumerate devices without starting the camera
            const devices = await navigator.mediaDevices.enumerateDevices();
            return devices.filter(device => device.kind === 'videoinput');
        } catch (error) {
            console.error("Error accessing devices:", error);
            return [];
        }
    }

    getCameraLabels(cameras: MediaDeviceInfo[]): string[] {
        let unnamedCounter = 1;
        return cameras.map(camera => {
            if (camera.label) {
                return camera.label;
            } else {
                // Add a numbered label for unnamed cameras
                return `Unnamed Camera ${unnamedCounter++}`;
            }
        });
    }

    // Function to update screen width and height
    updateScreenSize(): void {
        this.screenWidth = window.innerWidth;
        this.screenHeight = window.innerHeight;
        this.applyResponsiveLogic();
    }

    // Function to add responsive logic
    applyResponsiveLogic(): void {
        if (this.screenWidth < 768) {
            this.loggerService.debug('Mobile screen detected');
            this.cropRectangle = {
                x: 0,
                y: 0,
                width: 220,
                height: 220,
            }
            this.dialogWidth = '330px';
            this.dialogHeight = '330px';
            this.closeDialogLeft = '290px';
            this.actionsTopPercent = '84%';
        } else {
            this.dialogWidth = this.cropImage ? '350px' : '400px';
            this.dialogHeight = '390px';
            this.closeDialogLeft = this.cropImage ? '330px' : '375px';
            this.actionsTopPercent = '85%';

            this.cropRectangle = {
                x: 0,
                y: 0,
                width: 250,
                height: 250,
            }
        }
    }

    async initializeComponent() {
        const managerSettings = this.localStorageService.getSessionSettings();
        this.applyResponsiveLogic();
        if (this.selectedCurrentResult?.content == '') {
            this.selectedCurrentResult.content = this.imagePlaceholder;
        }
        if (managerSettings?.camerasFromKonektor) {
            this.getKonektorPropertiesUseCase.execute().subscribe({
                next: (data) => {
                    if (data) {
                        if (data.apiGatewayGrpc) {
                            this.localStorageService.setApiGatewayURL(data.apiGatewayGrpc);
                        }
                        else {
                            this.localStorageService.destroyApiGatewayURL();
                        }
                        if (data.maxSamples?.facial != 0) {
                            this.cameras = data.detectedDevices?.facial as string[];
                            this.showCameraDropdown = this.cameras.length > 1;
                            this.cameraForm.get('camera')?.setValue(this.cameras[0]);
                            if (this.canReadAndWrite && this.showCameraDialog && (this.replaceImage || this.selectedCurrentResult?.content == this.imagePlaceholder)) {
                                this.startCapture();
                            }
                            else {
                                this.loading = false;
                            }
                        }
                        else {
                            this.messageService.add({
                                severity: 'error',
                                summary: this.translateService.instant("content.errorTitle"),
                                detail: this.translateService.instant("messages.no_cam_detected"),
                                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                            });
                        }

                    } else {
                        this.messageService.add({
                            severity: 'error',
                            summary: this.translateService.instant("titles.konektor"),
                            detail: this.translateService.instant("messages.get_konektor_properties_error"),
                            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                        });
                    }
                },
                error: (e) => {
                    this.loggerService.error('Error Getting Konektor Properties:');
                    this.loggerService.error(e);
                    const at_attributes: ExtraData[] = [{ name: 'error', value: JSON.stringify(e) }];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_KONEKTOR_PROPERTIES, 0, 'ERROR', '', at_attributes);
                    this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('titles.konektor'),
                        detail: this.translateService.instant("messages.konektor_connection_error"),
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                }
            });
        }
        else {
            this.availableMediaCameras = await this.getAvailableCameras();
            this.cameras = this.getCameraLabels(this.availableMediaCameras);
            this.showCameraDropdown = this.cameras.length > 1;
            this.cameraForm.get('camera')?.setValue(this.cameras[0]);
            if (this.canReadAndWrite && this.showCameraDialog && (this.replaceImage || this.selectedCurrentResult?.content == this.imagePlaceholder)) {
                this.startVideo()
                .subscribe({
                    next: (stream: MediaStream) => {
                        this.captured = false;
                        this.capturing = true;
                        // Set the stream to the video element for display
                        this.mediaStream = stream;  // Store the MediaStream
                        this.videoElement.nativeElement.srcObject = this.mediaStream;
                    },
                    error: (e) => {
                        this.loggerService.error("Error during stream");
                        this.loggerService.error(e);
                    },
                    complete: () => {
                        this.loggerService.info("Stream completed");
                    }
                });
            }
            else {
                this.loading = false;
            }
        }
    }

    handleBeforeUnload(event: Event) {
        this.ngOnDestroy();
    }

    /* Camera Functions */

    startCapture() {
        this.loading = true;
        const camera = this.cameraForm.get('camera')?.value;
        if (!camera) {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant("content.errorTitle"),
                detail: this.translateService.instant("messages.no_cam_selected"),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
            this.loading = false;
            return;
        }
        this.takePhotoUseCase.execute({ camera: camera }).subscribe({
            next: async (reader: ReadableStreamDefaultReader<Uint8Array>) => {
                this.capturing = true;
                this.loading = false;
                const decoder = new TextDecoder();
                let ndjson = '';
                let done = false;
                const lineBuffer: string[] = [];
                while (!done) {
                    const { done: readDone, value } = await reader.read();
                    if (readDone) {
                        break;
                    }
                    if (this.cropImage) {
                        this.drawCrop();
                    }
                    const chunk = decoder.decode(value, { stream: true });
                    ndjson += chunk;
                    const lines = ndjson.split('\n');
                    ndjson = lines.pop() || '';
                    lineBuffer.push(...lines);
                    while (lineBuffer.length) {
                        const line = lineBuffer.shift();
                        if (line && line.trim()) {
                            try {
                                const message = JSON.parse(line) as KonektorResponseModel;
                                if (message.samples?.length == 1 && this.selectedCurrentResult) this.selectedCurrentResult.content = message.samples![0].content!;
                            } catch (error) {
                                this.loggerService.error("Error parsing konektor message");
                                this.loggerService.error(error!);
                            }
                        }
                    }
                }
            },
            error: (e) => {
                this.loggerService.error("Error taking photo");
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: 'error', value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.KONEKTOR_TAKE_PHOTO, 0, 'ERROR', '', at_attributes);
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('titles.konektor'),
                    detail: this.translateService.instant("messages.konektor_connection_error"),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
            }
        });
    }

    startVideo(): Observable<MediaStream> {
        let label = this.cameraForm.get('camera')?.value || this.cameras[0];
        if (!label) {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant("content.errorTitle"),
                detail: this.translateService.instant("messages.no_cam_selected"),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
            this.loading = false;
            return from(Promise.reject(new Error("Camera not found")));
        }
        const camera = this.availableMediaCameras.find(cam => cam.label === label || (!cam.label && label!.startsWith("Unnamed Camera")));
        if (!camera) {
            this.loggerService.error("Camera not found with label: " + label);
            this.loading = false;
            return from(Promise.reject(new Error("Camera not found")));
        }
        return from(
            navigator.mediaDevices.getUserMedia({
                video: {
                    deviceId: { ideal: camera.deviceId },
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                }
            }).then(stream => {
                this.loading = false;
                return stream;
            }).catch(error => {
                this.loggerService.error("Error accessing the camera for streaming:");
                this.loggerService.error(error);
                throw error;
            })
        );
    }

    drawCrop() {
        const context = this.cropCanvas.nativeElement.getContext('2d');
        const imgElement = this.cameraImg.nativeElement;
        const canvasElement = this.cropCanvas.nativeElement;

        if (!context || !imgElement) return;

        // Set the canvas dimensions to match the image
        canvasElement.width = imgElement.width;
        canvasElement.height = imgElement.height;

        // Clear any existing drawing on the canvas
        context.clearRect(0, 0, canvasElement.width, canvasElement.height);

        // Define the crop area as the clipping path
        context.save(); // Save the current state
        context.beginPath();
        context.rect(
            this.cropRectangle.x,
            this.cropRectangle.y,
            this.cropRectangle.width,
            this.cropRectangle.height
        );
        context.clip(); // Apply clipping to limit drawing to this region

        // Draw the image so only the cropped area is visible
        context.drawImage(
            imgElement,
            0,
            0,
            canvasElement.width,
            canvasElement.height
        );

        // Draw the outline for the crop rectangle (optional)
        context.beginPath();
        context.rect(
            this.cropRectangle.x,
            this.cropRectangle.y,
            this.cropRectangle.width,
            this.cropRectangle.height
        );
        context.lineWidth = 2;
        // context.strokeStyle = '#15FF1E';
        context.stroke();

        context.restore(); // Restore the context to remove clipping
    }

    async stopCapture() {
        await firstValueFrom(this.stopTakePhotoUseCase.execute());
        this.capturing = false;
    }

    stopVideo(): void {
        if (this.mediaStream) {
            // Stop each track of the media stream
            if (this.selectedCurrentResult) {
                this.selectedCurrentResult.content = this.captureImage();
            }
            this.mediaStream.getTracks().forEach(track => track.stop());
            this.mediaStream = undefined;  // Optionally reset the stream if you no longer need it
        } else {
            this.loggerService.error("No media stream to stop");
        }
    }

    // Method to crop image with proper scaling
    cropImg(): string | undefined {
        const imgElement = this.cameraImg.nativeElement;
        const { x, y, width, height } = this.cropRectangle;

        // Scaling factors for the original image dimensions
        const scaleX = imgElement.naturalWidth / imgElement.width;
        const scaleY = imgElement.naturalHeight / imgElement.height;

        // Adjust coordinates and dimensions to match the original image scale
        const adjustedX = x * scaleX;
        const adjustedY = y * scaleY;
        const adjustedWidth = width * scaleX;
        const adjustedHeight = height * scaleY;

        // Create an off-screen canvas to draw the cropped image
        const offScreenCanvas = document.createElement('canvas');
        const offScreenContext = offScreenCanvas.getContext('2d')!;

        // Set the off-screen canvas dimensions to match the cropped area
        offScreenCanvas.width = adjustedWidth;
        offScreenCanvas.height = adjustedHeight;

        // Draw the adjusted area onto the off-screen canvas
        offScreenContext.drawImage(
            imgElement,
            adjustedX, adjustedY, adjustedWidth, adjustedHeight, // Source coordinates
            0, 0, adjustedWidth, adjustedHeight                  // Destination coordinates
        );

        // Return the cropped image data URL
        return offScreenCanvas.toDataURL('image/jpeg')?.replace('data:image/jpeg;base64,', '');
    }

    captureImage() {
        const video: HTMLVideoElement = this.videoElement.nativeElement;
        const canvas: HTMLCanvasElement = this.canvasElement.nativeElement;
        const img: HTMLImageElement = this.capturedImage.nativeElement;

        // Set canvas dimensions to match the video
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        // Draw video frame to canvas
        const context = canvas.getContext('2d');
        if (context) {
            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            // Get image data from canvas
            return canvas.toDataURL('image/png');
        }
        else {
            return '';
        }
    }

    /* Component Actions */

    // Take Photo
    async onSubmitAdd() {
        if (!this.capturing) {
            // Start capturing
            this.captured = false;
            this.startVideo();
        }
        else {
            // Stop capturing
            this.captured = true;
            await this.stopVideo();
        }
    }

    // Create
    onSubmitCreate() {
        if (this.selectedCurrentResult) {
            if (this.cropImage) {
                this.selectedCurrentResult.content = this.cropImg();
            }
            this.result.emit({ action: 'create', staticResource: { ... this.selectedCurrentResult } });
        }
        else {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant("content.errorTitle"),
                detail: this.translateService.instant("messages.no_data_to_save"),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
        }
    }

    // Delete
    confirmDelete() {
        this.confirmationService.confirm({
            message: `${this.translateService.instant('messages.delete_single_record')} <b>${this.selectedCurrentResult?.name + '-' + this.selectedCurrentResult?.number}</b>?`,
            header: this.translateService.instant('messages.delete_confirmation_header'),
            icon: 'pi pi-exclamation-triangle',
            rejectButtonStyleClass: "p-button-text",
            acceptButtonStyleClass: "ng-confirm-button",
            acceptIcon: "none",
            rejectIcon: "none",
            acceptLabel: this.translateService.instant("delete"),
            rejectLabel: this.translateService.instant("no"),
            accept: () => {
                this.result.emit({ action: 'delete', staticResource: { ... this.selectedCurrentResult } });
            },
            reject: () => {
                this.resetInactivityMonitor();
            }
        });

        this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
        this.startCheckingInactivity = false;
        setTimeout(() => this.startCheckingInactivity = true, 0);
    }

    // Close
    async closeDialog() {
        this.captured = false;
        this.stopVideo();
        this.result.emit({ action: 'close', staticResource: { ... this.selectedCurrentResult } });
    }

    closeConfirmationDialog() {
        this.confirmationService.close();
        this.resetInactivityMonitor();
    }
}