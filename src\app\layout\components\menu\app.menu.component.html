<div class="flex flex-column justify-content-between" [style]="{'height':'100%'}">
    <ul class="layout-menu">
        <ng-container *ngFor="let item of model; let i = index;">
            <li app-menuitem *ngIf="!item.separator" [item]="item" [index]="i" [root]="true"></li>
            <li *ngIf="item.separator" class="menu-separator mx-3 mt-3"></li>
        </ng-container>
    </ul>
    <div class="flex justify-content-center pb-4 pt-4">
        <div class="flex justify-content-center align-items-center gap-2">
            <img src="verazial-common-frontend/assets/images/admin/appLogoMini.svg" [style]="{height: '35px'}"/>
            <label class="version-text pb-1" for="">V{{version}}</label>
        </div>
    </div>
</div>
