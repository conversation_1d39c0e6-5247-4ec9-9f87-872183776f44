import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { Table } from "jspdf-autotable";
import { ConfirmationService, FilterService, MenuItem, MessageService, TreeNode } from "primeng/api";
import { environment } from "src/environments/environment";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { OperationType } from "src/verazial-common-frontend/core/general/assignment/categories/common/enum/operation-type.enum";
import { ScheduleCustom } from "src/verazial-common-frontend/core/general/assignment/categories/common/interfaces/schedule-custom.interface";
import { ScheduleTypeEnum } from "src/verazial-common-frontend/core/general/assignment/categories/common/interfaces/schedule-type.enum";
import { RoleEntity } from "src/verazial-common-frontend/core/general/common/entity/role.entity";
import { AttributeData } from "src/verazial-common-frontend/core/general/flow/common/models/attribute-data.model";
import { GetKonektorPropertiesUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { NewLocationModel } from "src/verazial-common-frontend/core/general/manager/common/models/new-location.model";
import { GetSettingsByApplicationUseCase } from "src/verazial-common-frontend/core/general/manager/domain/use-cases/get-settings-by-application.use-case";
import { AuthScheduleDetailEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/auth-schedules/auth-schedule-detail.entity";
import { AuthScheduleEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/auth-schedules/auth-schedule.entity";
import { DayTimeAuthScheduleDetailEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/auth-schedules/day-time-auth-schedule-detail.entity";
import { CreateAuthScheduleUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/auth-schedules/create-auth-schedule.use-case";
import { DeleteAuthScheduleByIdUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/auth-schedules/delete-auth-schedule-by-id.use-case";
import { GetAllAuthSchedulesUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/auth-schedules/get-all-auth-schedules.use-case";
import { GetAuthScheduleByIdUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/auth-schedules/get-auth-schedule-by-id.use-case";
import { UpdateAuthScheduleUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/auth-schedules/update-auth-schedule-by-id.use-case";
import { RoleType } from "src/verazial-common-frontend/core/general/role/common/enum/role-type.enum";
import { RoleWithAccessEntity } from "src/verazial-common-frontend/core/general/role/domain/entity/role-with-access.entity";
import { GetAllRolesUseCase } from "src/verazial-common-frontend/core/general/role/domain/use-cases/roles/get-all-roles.use-case";
import { AccessIdentifier } from "src/verazial-common-frontend/core/models/access-identifier.enum";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { OperationStatus } from "src/verazial-common-frontend/core/models/operation-status.interface";
import { Status } from "src/verazial-common-frontend/core/models/status.enum";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { CheckPermissionsService } from "src/verazial-common-frontend/core/services/check-permissions-service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { NewLocationsService } from "src/verazial-common-frontend/core/services/new-locations.service";
import { ValidatorService } from "src/verazial-common-frontend/modules/shared/services/validator.service";

@Component({
    selector: 'app-auth-schedules',
    templateUrl: './auth-schedules-list.component.html',
    styleUrl: './auth-schedules-list.component.css'
})
export class AuthSchedulesListComponent implements OnInit, OnChanges, OnDestroy {

    // Inputs
    @Input() userIsVerified: boolean = false;

    isLoading: boolean = false;
    modified: boolean = false;

    confirmDialogTimeoutLimit: number = 0;
    startCheckingInactivity: boolean = false;

    canReadAndWrite: boolean = false;
    canReadAndWriteSchedule: boolean = false;
    canSeeAllLocations: boolean = false;
    readOnly: boolean = false;
    access_identifier: string = AccessIdentifier.PRISONS_AUTHORIZATION_SCHEDULES;
    all_locations_idenfitier = AccessIdentifier.GET_ALL_LOCATIONS;
    isDisabledSaveButton: boolean = true;

    listOfAuthSchedules: AuthScheduleEntity[] = [];
    selectedAuthSchedules: AuthScheduleEntity[] = [];

    // Create/Update Dialog
    showAuthScheduleDialog: boolean = false;
    authSchedule?: AuthScheduleEntity;
    operationType!: OperationType;
    opType = OperationType;
    createUpdateButtonTitle: string = this.translateService.instant('save');

    // Options
    listRoles: RoleEntity[] = [];
    selectedRole?: RoleEntity;
    listLocations: TreeNode[] = [];
    selectedLocation?: TreeNode;
    listScheduleTypes: AttributeData[] = [
        { key: ScheduleTypeEnum.ALL, value: 'content.everyday' },
        { key: ScheduleTypeEnum.WORKING_DAYS, value: 'content.monday_to_friday' },
        { key: ScheduleTypeEnum.CUSTOM, value: 'content.customised' }
    ]
    // Table Filters Filter
    filteredValues: any[] = [];
    formDateFilter: FormGroup = new FormGroup({
        date: new FormControl<Date[] | null>(null)
    });
    dateFilterValues = {
        startDate: null,
        endDate: null
    };
    rangeDates: Date[] | null = null;

    // Settings
    managerSettings?: GeneralSettings;

    constructor(
        private confirmationService: ConfirmationService,
        private filterService: FilterService,
        private localStorageService: LocalStorageService,
        private translateService: TranslateService,
        private loggerService: ConsoleLoggerService,
        private messageService: MessageService,
        private auditTrailService: AuditTrailService,
        private checkPermissions: CheckPermissionsService,
        private newLocationsService: NewLocationsService,
        private getAllRolesUseCase: GetAllRolesUseCase,
        private getSettingsByApplicationUseCase: GetSettingsByApplicationUseCase,
        private getAllAuthSchedulesUseCase: GetAllAuthSchedulesUseCase,
        private deleteAuthScheduleByIdUseCase: DeleteAuthScheduleByIdUseCase,
        private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
    ) {
        this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
            if (!filter || (!filter.startDate && !filter.endDate)) {
                return true; // If no filter, show all
            }
            const dateValue = new Date(value).getTime();
            const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
            const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
            if (startDate && endDate) {
                return dateValue >= startDate && dateValue <= endDate;
            } else if (startDate) {
                return dateValue >= startDate;
            } else if (endDate) {
                return dateValue <= endDate;
            }
            return false;
        });
    }

    ngOnInit() {
        this.isLoading = true;
        this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
        this.canSeeAllLocations = this.checkPermissions.hasReadPermissions(this.all_locations_idenfitier);
        this.getManagerSettings();
        this.getAllRoles();
        this.getData();
    }

    ngOnChanges() { }

    ngOnDestroy() {
        // Clean up the timeout if the component is destroyed
        this.closeConfirmationDialog();
    }

    resetInactivityMonitor() {
        this.startCheckingInactivity = false;
        this.confirmDialogTimeoutLimit = 0;
    }

    getManagerSettings() {
        this.getSettingsByApplicationUseCase.execute({ applicationName: environment.application }).then(
            (data) => {
                this.managerSettings = data.settings!;
            },
            (e) => {
                this.loggerService.error(e);
                this.managerSettings = this.localStorageService.getSessionSettings()!;
            }
        )
            .finally(() => {
                // this.loggerService.debug(this.managerSettings?.continued1?.newLocations);
                if (this.managerSettings) {
                    let options = this.managerSettings.continued1?.newLocations!;
                    if (this.canSeeAllLocations)
                        this.listLocations = this.newLocationsService.locationsToTreeSelectOptions(options, false);
                    else {
                        this.getKonektorPropertiesUseCase.execute().subscribe({
                            next: (data) => {
                                const konektorProperties = data;
                                if (data.apiGatewayGrpc) {
                                    this.localStorageService.setApiGatewayURL(data.apiGatewayGrpc);
                                }
                                else {
                                    this.localStorageService.destroyApiGatewayURL();
                                }
                                var location = konektorProperties.locationId;
                                this.listLocations = this.newLocationsService.locationsToTreeSelectOptionsForLocation(options, location!, false);
                            },
                            error: (e) => {
                                this.loggerService.error('Error Getting Konektor Properties:');
                                this.loggerService.error(e);
                            }
                        });
                    }
                }
            });
    }

    getData() {
        this.isLoading = true;
        this.getAllAuthSchedulesUseCase.execute({}).then(
            (data) => {
                this.loggerService.debug(data)
                this.listOfAuthSchedules = data;
            },
            (e) => {
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_AUTH_SCHEDULES, 0, 'ERROR', '', at_attributes);
            }
        )
            .finally(() => {
                this.isLoading = false;
            });
    }

    getAllRoles() {
        this.getAllRolesUseCase.execute().then(
            (data) => {
                data.forEach(role => {
                    if (role.type == RoleType.SUBJECT) {
                        this.listRoles.push({
                            id: role.id,
                            name: role.name == 'SYSTEM_USER' ? this.translateService.instant('role_names.SYSTEM_USER') : role.name,
                            level: role.level,
                            type: role.type,
                            description: role.description,
                            showInMenu: role.showInMenu,
                            createdAt: undefined,
                            updatedAt: undefined,
                        });
                    }
                });
            },
            (e) => {
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_ROLES, 0, 'ERROR', '', at_attributes);
            },
        );
    }

    onNewAuthSchedule() {
        this.operationType = OperationType.INSERT;
        this.createUpdateButtonTitle = this.translateService.instant('save');
        this.canReadAndWriteSchedule = this.canReadAndWrite;
        this.showAuthScheduleDialog = true;
    }

    onEditAuthSchedule(authSchedule: AuthScheduleEntity) {
        if(!authSchedule) {
            if (this.selectedAuthSchedules.length === 1) {
                authSchedule = this.selectedAuthSchedules[0];
            }
            else {
                return;
            }
        }
        this.authSchedule = {...authSchedule};
        this.operationType = OperationType.UPDATE;
        this.createUpdateButtonTitle = this.translateService.instant('update');
        this.canReadAndWriteSchedule = this.canReadAndWrite && this.getCanReadAndWriteSchedule(this.authSchedule);
        this.showAuthScheduleDialog = true;
    }

    async deleteMultipleAuthSchedules() {
        if (this.selectedAuthSchedules.length > 0) {
            this.confirmationService.confirm({
                message: this.translateService.instant('messages.message_remove') + " <b>" + this.selectedAuthSchedules.length + `</b>?`,
                header: this.translateService.instant('messages.delete_multiple_records'),
                icon: 'pi pi-exclamation-triangle',
                acceptIcon: "none",
                rejectIcon: "none",
                rejectButtonStyleClass: "p-button-text",
                acceptButtonStyleClass: "ng-confirm-button",
                acceptLabel: this.translateService.instant('delete'),
                rejectLabel: this.translateService.instant('no'),
                accept: async () => {
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(this.selectedAuthSchedules) },
                    ];

                    this.auditTrailService.auditTrailSelectReason(
                        ReasonTypeEnum.CONFIG,
                        AuditTrailActions.DEL_AUTH_SCHEDULE,
                        ReasonActionTypeEnum.DELETE,
                        async () => {
                            this.isLoading = true;
                            try {
                                // Create an array of deletion promises
                                const deletePromises = this.selectedAuthSchedules.map(authSchedule =>
                                    this.deleteAuthScheduleById(authSchedule)
                                );

                                // Wait for all deletions to complete
                                await Promise.all(deletePromises);

                                // Success Message
                                this.messageService.add({
                                    severity: 'success',
                                    summary: this.translateService.instant('content.successTitle'),
                                    detail: this.translateService.instant('messages.success_general'),
                                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                                });

                                // Clear selected items
                                this.selectedAuthSchedules = [];

                            } catch (error: any) {
                                // Handle any errors that occur during deletion
                                this.messageService.add({
                                    severity: 'error',
                                    summary: this.translateService.instant('content.errorTitle'),
                                    detail: `${this.translateService.instant('messages.error_deleting_auth_schedule')}: ${error.message}`,
                                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                                });

                                this.loggerService.error('Error deleting authentication schedules:');
                                this.loggerService.error(error);
                            } finally {
                                this.isLoading = false;
                            }
                        },
                        at_attributes
                    );
                },
                reject: () => {
                    this.resetInactivityMonitor();
                }
            });

            this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
            this.startCheckingInactivity = false;
            setTimeout(() => this.startCheckingInactivity = true, 0);
        }
    }

    deleteAuthSchedule(data: AuthScheduleEntity) {
        this.confirmationService.confirm({
            message: this.translateService.instant('messages.message_remove') + " <b>" + data.name + `</b>?`,
            header: this.translateService.instant('messages.delete_single_record'),
            icon: 'pi pi-exclamation-triangle',
            acceptIcon: "none",
            rejectIcon: "none",
            rejectButtonStyleClass: "p-button-text",
            acceptButtonStyleClass: "ng-confirm-button",
            acceptLabel: this.translateService.instant('delete'),
            rejectLabel: this.translateService.instant('no'),
            accept: () => {
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
                ];
                this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.DEL_AUTH_SCHEDULE, ReasonActionTypeEnum.DELETE, () => { this.deleteAuthScheduleById(data); }, at_attributes);
            },
            reject: () => {
                this.resetInactivityMonitor();
            }
        });

        this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
        this.startCheckingInactivity = false;
        setTimeout(() => this.startCheckingInactivity = true, 0);
    }

    deleteAuthScheduleById(data: AuthScheduleEntity): Promise<void> {
        return new Promise((resolve, reject) => {
            const id = data.id!;
            this.deleteAuthScheduleByIdUseCase.execute({ id: id }).then(
                () => {
                    this.listOfAuthSchedules = this.listOfAuthSchedules.filter(schedule => schedule.id !== id);
                    resolve(); // Resolve the promise when the deletion is successful
                },
                (e) => {
                    this.messageService.add({
                        severity: 'error',
                        summary: `${this.translateService.instant("titles.error_operation")}: ${data.name}`,
                        detail: `${this.translateService.instant("messages.error_deleting_auth_schedule")}: ${e.message}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });

                    this.loggerService.error(e);

                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
                    ];

                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_AUTH_SCHEDULE, 0, 'ERROR', '', at_attributes);

                    reject(e); // Reject the promise so the caller (Promise.all) can handle the error
                }
            );
        });
    }

    operationStatus(event: OperationStatus) {
        if (event.status == Status.SUCCESS) {
            this.showAuthScheduleDialog = false;
            if (event.message != 'CLOSE') {
                this.messageService.add({
                    severity: 'success', summary: this.translateService.instant("titles.success_operation"), detail: this.translateService.instant("messages.success_general"),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.getData();
            }
        } else {
            this.messageService.add({
                severity: 'error', summary: this.translateService.instant("titles.error_operation"), detail: `${event.message}`,
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
        }
    }

    getScheduleType(scheduleType: string): string {
        return this.listScheduleTypes.find((type) => type.key === scheduleType)?.value?.toString() || '';
    }

    getRoleName(roleId: number): string {
        return this.listRoles.find((role) => role.id === roleId)?.name || '';
    }

    getRoleNames(roleIds: number[]): string {
        return this.listRoles
            .filter((role) => roleIds.includes(role.id!))
            .map((role) => role.name)
            .join(', ') || '';
    }

    getLocationPath(locationId: string): string {
        return this.newLocationsService.getNameAndTree(locationId, this.listLocations)!;
    }

    getLocationPathReduce(locationIds: string[]): string {
        return this.newLocationsService.getNames(locationIds, this.listLocations)!.join(", ");
    }

    onFilter(event: any, dt: Table) {
        this.filteredValues = event.filteredValue;
        console.log(event.filters);
        if (!event.filters['updatedAt']?.value) {
            this.rangeDates = null;
            this.formDateFilter.reset();
        }
    }

    isValidDate(dateString: string): boolean {
        const date = new Date(dateString);
        return !isNaN(date.getTime()) && date.toISOString().split('T')[0] != (new Date(0)).toISOString().split('T')[0];
    }

    getCanReadAndWriteSchedule(authSchedule: AuthScheduleEntity): boolean {
        return this.newLocationsService.containsAny(authSchedule.locationId!, this.listLocations) || this.canSeeAllLocations;
    }

    closeConfirmationDialog() {
        this.confirmationService.close();
        this.resetInactivityMonitor();
    }
}