import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ValidatorService } from 'src/verazial-common-frontend/modules/shared/services/validator.service';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { DataSourceParametersEntity } from 'src/verazial-common-frontend/core/general/data-source/domain/entities/data-source-parameters.entity';
import { DataSourceEntity } from 'src/verazial-common-frontend/core/general/data-source/domain/entities/data-source.entity';
import { AddAppDataSourceParamsUseCase } from 'src/verazial-common-frontend/core/general/data-source/domain/use-cases/add-app-data-source-params.use-case';
import { DeleteDataSourceByIdUseCase } from 'src/verazial-common-frontend/core/general/data-source/domain/use-cases/delete-data-source-by-id.use-case';
import { DeleteDataSourceParamsByIdUseCase } from 'src/verazial-common-frontend/core/general/data-source/domain/use-cases/delete-data-source-params-by-id.use-case';
import { GetAllParamsByDataSourceIdUseCase } from 'src/verazial-common-frontend/core/general/data-source/domain/use-cases/get-all-params-by-data-source-id.use-case';
import { GetAppDataSourceParamByIdUseCase } from 'src/verazial-common-frontend/core/general/data-source/domain/use-cases/get-app-data-source-param-by-id.use-case';
import { UpdateAppDataSourceByIdUseCase } from 'src/verazial-common-frontend/core/general/data-source/domain/use-cases/update-app-data-source-by-id.use-case';
import { UpdateAppDataSourceParamByIdUseCase } from 'src/verazial-common-frontend/core/general/data-source/domain/use-cases/update-app-data-source-param-by-id.use-case';
import { AttributeData } from 'src/verazial-common-frontend/core/models/attribute-data.model';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { OperationStatus } from 'src/verazial-common-frontend/core/models/operation-status.interface';
import { SourceType } from 'src/verazial-common-frontend/core/models/source-type.enum';
import { Status } from 'src/verazial-common-frontend/core/models/status.enum';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'app-list-data-sources',
  templateUrl: './list-data-sources.component.html',
  styleUrl: './list-data-sources.component.css',
  providers: [MessageService, ConfirmationService]
})
export class ListDataSourcesComponent implements OnInit, OnDestroy {

  @Input() readAndWritePermissions: boolean = false;
  @Input() inputData: DataSourceEntity[] = []
  @Output() outputData = new EventEmitter<boolean>();

  loading: boolean = false;
  showNewDataSourceDialog: boolean = false;
  showEditDataSourceDialog: boolean = false;
  showEditParametersDialog: boolean = false;
  showListParametersDialog: boolean = false;

  selectedDataSource: DataSourceEntity | undefined;

  selectedSourceType: AttributeData | undefined;
  listDataSourceParameters: DataSourceParametersEntity[] = [];

  newListDataSourceParameters: DataSourceParametersEntity[] = [];

  searchValue: string | undefined;

  confirmDialogTimeoutLimit: number = 0;
  startCheckingInactivity: boolean = false;

  listSourceTypes = [
    // {key: SourceType.WEBSERVICE, value: 'Web Service'},
    {key: SourceType.API_REST, value: 'API Rest'},
    {key: SourceType.API_VERAZIAL, value: 'API Verázial'},
    {key: SourceType.LDAP, value: 'LDAP'},
    {key: SourceType.LDAP_VERAZIAL, value: 'LDAP + Verázial'},
    // {key: SourceType.DB_VIEW, value: 'DB View'},
    // {key: SourceType.DATABASE, value: 'Database'},
    // {key: SourceType.STORE_PROCEDURE, value: 'Store Procedure'}
    ];

  constructor(
    private fb: FormBuilder,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private translateService: TranslateService,
    private deleteDataSourceByIdUseCase: DeleteDataSourceByIdUseCase,
    private updateAppDataSourceByIdUseCase: UpdateAppDataSourceByIdUseCase,
    private validatorService: ValidatorService,
    private getAllParamsByDataSourceIdUseCase: GetAllParamsByDataSourceIdUseCase,
    private addAppDataSourceParamsUseCase: AddAppDataSourceParamsUseCase,
    private updateAppDataSourceParamByIdUseCase: UpdateAppDataSourceParamByIdUseCase,
    private deleteDataSourceParamsByIdUseCase: DeleteDataSourceParamsByIdUseCase,
    private getAppDataSourceParamByIdUseCase: GetAppDataSourceParamByIdUseCase,
    private localStorageService: LocalStorageService,
    private auditTrailService: AuditTrailService,
    private loggerService: ConsoleLoggerService,
  ){}

  public form: FormGroup = this.fb.group({
    dataSourceName: ['', [Validators.required]],
    sourceType: ['', [Validators.required]],
    method: ['', [Validators.required]],
  });

  ngOnInit(): void {
  }

  ngOnDestroy(): void {
    // Clean up the timeout if the component is destroyed
    this.closeConfirmationDialog();
  }

  resetInactivityMonitor() {
    this.startCheckingInactivity = false;
    this.confirmDialogTimeoutLimit = 0;
  }

  createNewDataSource(){
    this.showNewDataSourceDialog = true;
  }

  editDataSource(data: DataSourceEntity){
    this.selectedDataSource = data;
    this.showEditDataSourceDialog = true;
    this.form.get('dataSourceName')?.setValue(data.name);
    this.form.get('method')?.setValue(data.method);
    this.selectedSourceType = this.listSourceTypes.find(type => type.key == data.sourceType);
    if (!this.readAndWritePermissions) {
      this.form.disable();
    }
    else {
      this.form.enable();
    }
  }

  isValid(field: string): boolean {
    return this.validatorService.isValidField(this.form, field);
  }

  confirmationDeleteDataSource(data: DataSourceEntity){
    this.confirmationService.confirm({
      message: this.translateService.instant('messages.message_remove') + '<b>' + data.name + '</b>?',
      header: this.translateService.instant("pass_datasource.remove_data_source"),
      icon: 'pi pi-exclamation-triangle',
      acceptIcon:"none",
      rejectIcon:"none",
      rejectButtonStyleClass:"p-button-text",
      acceptButtonStyleClass:"ng-confirm-button",
      acceptLabel: this.translateService.instant("yes"),
      rejectLabel: this.translateService.instant("no"),
      accept: () => {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
        ];
        this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.DEL_DS, ReasonActionTypeEnum.DELETE, () => { this.deleteDataSource(data); }, at_attributes);
      },
      reject: () => {
        this.resetInactivityMonitor();
      }
    });

    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }

  deleteDataSource(data: DataSourceEntity){
    this.deleteDataSourceByIdUseCase.execute({id: data.id}).then(
      () => {
        this.inputData = [...this.inputData.filter((_v, k) => _v.id !== data.id)];
        this.messageService.add({
          severity: 'success',
          summary: this.translateService.instant('content.successTitle'),
          detail: this.translateService.instant('messages.removed_successfully'),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translateService.instant('content.errorTitle'),
          detail: String(e.message),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_DS, 0, 'ERROR', '', at_attributes);
      },
    );
  }

  cancelNewDataSource(){
    this.showNewDataSourceDialog = false;
  }

  cancelEditDataSource(){
    this.showEditDataSourceDialog = false;
    this.showEditParametersDialog = false;
  }

  operationResult(result: OperationStatus){
    this.loggerService.error(result);
    if(result.status == Status.SUCCESS){
      this.messageService.add({
        severity: 'success',
        summary: this.translateService.instant('content.successTitle'),
        detail: this.translateService.instant('messages.saved_successfully'),
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });
      this.showNewDataSourceDialog = false;
      this.outputData.emit(true);
    }else{
      this.messageService.add({
        severity: 'error',
        summary: this.translateService.instant('content.errorTitle'),
        detail: result.message,
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });

    }
  }

  confirmationUpdateDataSource(){
    this.confirmationService.confirm({
      message: this.translateService.instant('messages.message_update_data_source') + '<b>' + this.selectedDataSource?.name + '</b>?',
      header: this.translateService.instant("confirmation"),
      icon: 'pi pi-exclamation-triangle',
      acceptIcon:"none",
      rejectIcon:"none",
      rejectButtonStyleClass:"p-button-text",
      acceptButtonStyleClass:"ng-confirm-button",
      acceptLabel: this.translateService.instant("yes"),
      rejectLabel: this.translateService.instant("no"),
      accept: () => {
        this.updateDataSource();
      },
      reject: () => {
        this.resetInactivityMonitor();
      }
    });

    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }

  confirmationUpdateDataSourceParameters(){
    this.confirmationService.confirm({
      message: this.translateService.instant('messages.message_add_parameters') + '<b>' + this.selectedDataSource?.name + '</b>?',
      header: this.translateService.instant("confirmation"),
      icon: 'pi pi-exclamation-triangle',
      acceptIcon:"none",
      rejectIcon:"none",
      rejectButtonStyleClass:"p-button-text",
      acceptButtonStyleClass:"ng-confirm-button",
      acceptLabel: this.translateService.instant("yes"),
      rejectLabel: this.translateService.instant("no"),
      accept: () => {
        this.saveDataSourceParameters();
      },
      reject: () => {
        this.resetInactivityMonitor();
      }
    });

    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }


  updateDataSource(){
    let datasource: DataSourceEntity = {
      id: this.selectedDataSource?.id!,
      name: this.form.get('dataSourceName')?.value,
      sourceType: this.form.get('sourceType')?.value.key,
      method: this.form.get('method')?.value
    }
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(this.selectedDataSource) },
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(datasource) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.MOD_DS, ReasonActionTypeEnum.UPDATE, () => {
      this.updateAppDataSourceByIdUseCase.execute(datasource).then(
        ()=>{
          this.messageService.add({
            severity: 'success',
            summary: this.translateService.instant('content.successTitle'),
            detail: this.translateService.instant('messages.updated_successfully'),
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.showEditDataSourceDialog = false;
          this.outputData.emit(true);
        },
        (e)=>{
          this.messageService.add({
            severity: 'error',
            summary: this.translateService.instant('content.errorTitle'),
            detail: e.message,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(this.selectedDataSource) },
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(datasource) },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_DS, 0, 'ERROR', '', at_attributes);
        }
      );
    }, at_attributes);
  }

  showParameters(){
    this.showListParametersDialog = true;
  }

  updateDataSourceParameters(){

  }

  getParameters(data: DataSourceEntity){
    this.showEditParametersDialog = true;
    this.selectedDataSource = data;
    this.getAllParamsByDataSourceIdUseCase.execute({dataSourceId: data.id}).then(
      (parameters)=>{
        this.listDataSourceParameters = parameters;
      },
      (e)=>{
        this.messageService.add({
          severity: 'error',
          summary: this.translateService.instant('content.errorTitle'),
          detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_PARAMS_BY_DATA_SOURCE_ID, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  addNewParameters(parameter: DataSourceParametersEntity){
    // this.listDataSourceParameters.push(parameter);
    this.listDataSourceParameters = [...this.listDataSourceParameters, parameter];
    // this.newListDataSourceParameters.push(parameter);
    this.newListDataSourceParameters = [...this.newListDataSourceParameters, parameter];
  }

  saveDataSourceParameters(){
    let parameters = this.newListDataSourceParameters.map(obj => {
      return { ...obj, dataSourceId: this.selectedDataSource?.id };
    });

    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(parameters) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.ADD_DS_PARAM, ReasonActionTypeEnum.CREATE, () => {
      this.addAppDataSourceParamsUseCase.execute(parameters).then(
        () => {
          this.messageService.add({
            severity: 'success',
            summary: this.translateService.instant('content.successTitle'),
            detail: this.translateService.instant('messages.saved_successfully'),
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.showEditParametersDialog = false;
        },
        (e) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translateService.instant('content.errorTitle'),
            detail: `${this.translateService.instant('messages.error_save')}: ${e.message}`,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(parameters) },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_DS_PARAM, 0, 'ERROR', '', at_attributes);
        }
      );
    }, at_attributes);
  }

  deleteParameter(parameter: DataSourceParametersEntity){
    if(parameter.dataSourceId){
      const at_attributes: ExtraData[] = [
        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(parameter) },
      ];
      this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.DEL_DS_PARAM, ReasonActionTypeEnum.DELETE, () => {
        this.deleteDataSourceParamsByIdUseCase.execute({id: parameter.id}).then(
          () => {
            this.listDataSourceParameters = [...this.listDataSourceParameters.filter(param=> param.id != parameter.id)];
          },
          (e) => {
            this.loggerService.error(e);
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(parameter) },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_DS_PARAM, 0, 'ERROR', '', at_attributes);
          }
        );
      }, at_attributes);
    }

    this.newListDataSourceParameters = [...this.newListDataSourceParameters.filter(param=> param.id != parameter.id)];

  }

  updateParameter(parameter: DataSourceParametersEntity){
    if(parameter.dataSourceId){
      this.getAppDataSourceParamByIdUseCase.execute({id: parameter.id}).then(
        (data)=>{
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(parameter) },
          ];
          this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.MOD_DS_PARAM, ReasonActionTypeEnum.UPDATE, () => {
            this.updateAppDataSourceParamByIdUseCase.execute(parameter).then(
              (response)=>{
              },
              (e)=>{
                const at_attributes: ExtraData[] = [
                  { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
                  { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(parameter) },
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_DS_PARAM, 0, 'ERROR', '', at_attributes);
              }
            );
          }, at_attributes);
        },
        (e)=>{
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_APP_DATA_SOURCE_PARAM_BY_ID, 0, 'ERROR', '', at_attributes);
        }
      );
    }
  }

  getApiTypeName(value: string){
    return this.listSourceTypes.filter(type => type.key == value)[0].value
  }

  closeConfirmationDialog() {
    this.confirmationService.close();
    this.resetInactivityMonitor();
  }
}