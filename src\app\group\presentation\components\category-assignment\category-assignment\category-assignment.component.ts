import { Component, EventEmitter, Input, On<PERSON><PERSON><PERSON>, OnDestroy, OnInit, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MessageService } from 'primeng/api';
import { PickListMoveAllToTargetEvent, PickListMoveToTargetEvent } from 'primeng/picklist';
import { elementAt } from 'rxjs';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { GroupCategoryType } from 'src/verazial-common-frontend/core/general/assignment/categories/common/models/group-category-type.enum';
import { GroupCategoryEntity } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/entity/group-category.entity';
import { GetAllGroupsCategoriesUseCase } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/use-cases/get-all-groups-categories.use-case';
import { AssignmentElementEntity } from 'src/verazial-common-frontend/core/general/assignment/group/domain/entity/assignment-elements.entity';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { AuditTrailService } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'app-category-assignment',
  templateUrl: './category-assignment.component.html',
  styleUrl: './category-assignment.component.css'
})
export class CategoryAssignmentComponent implements OnInit, OnDestroy {
  @Input() canReadAndWrite: boolean = false;
  @Input() inputData: AssignmentElementEntity[] = [];
  @Input() filterType: GroupCategoryType[] = [];
  @Input() limit: number = 0;
  @Input() limitErrorMessage: string = "";
  @Output() listCategories = new EventEmitter<GroupCategoryEntity[]>();

  targetListCategories: GroupCategoryEntity[] = [];
  sourceListCategories: GroupCategoryEntity[] = [];

  categoryTypes = GroupCategoryType;

  constructor(
    private getAllGroupsCategoriesUseCase: GetAllGroupsCategoriesUseCase,
    private translateService: TranslateService,
    private loggerService: ConsoleLoggerService,
    private auditTrailService: AuditTrailService,
    private localStorageService: LocalStorageService,
    private messageService: MessageService,
  ) { }

  ngOnDestroy(): void {
    this.targetListCategories = [];
    this.sourceListCategories = [];
  }

  ngOnInit(): void {
    this.getAllGroupsCategories();
    /*if(this.inputData){
      this.targetListCategories = this.inputData;
    }*/
  }

  getAllGroupsCategories() {
    this.getAllGroupsCategoriesUseCase.execute().then(
      (categories) => {
        this.sourceListCategories = categories
        if (this.filterType.length > 0) {
          this.sourceListCategories = this.sourceListCategories.filter((s) => this.filterType.some(element => s.type == element));
        }
        if (this.inputData.length > 0) {
          this.targetListCategories = this.sourceListCategories.filter((s) => this.inputData.some(element => s.id == element.elementId));
          this.targetListCategories.map((t) => {
            this.sourceListCategories = this.sourceListCategories.filter((s) => t.id != s.id)
          });
        }
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_GROUP_CATEGORIES, 0, 'ERROR', '', at_attributes);
      })
  }

  targetTrackChanges() {
    this.listCategories.emit(this.targetListCategories);
  }

  targetTrackChangesToTarget(event: PickListMoveToTargetEvent) {
    if (this.targetListCategories.length > this.limit && this.limit != 0) {
      this.targetListCategories = this.targetListCategories.filter((t) => t.id != event.items[0].id);
      this.sourceListCategories.push(event.items[0]);
      const message = this.limitErrorMessage != '' ? this.translateService.instant(this.limitErrorMessage) : this.translateService.instant('content.limitReached');
      this.loggerService.error(message);
      this.messageService.add({
        severity: 'error',
        summary: this.translateService.instant('titles.error'),
        detail: message,
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      })
      return;
    }
    this.listCategories.emit(this.targetListCategories);
  }

  targetTrackChangesAllToTarget(event: PickListMoveAllToTargetEvent) {
    if (event.items.length > this.limit && this.limit != 0) {
      event.items.map((i) => {
        this.targetListCategories = this.targetListCategories.filter((t) => t.id != i.id);
        this.sourceListCategories.push(i);
      });
      const message = this.limitErrorMessage != '' ? this.translateService.instant(this.limitErrorMessage) : this.translateService.instant('content.limitReached');
      this.loggerService.error(message);
      this.messageService.add({
        severity: 'error',
        summary: this.translateService.instant('titles.error'),
        detail: message,
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      })
      return;
    }
    this.listCategories.emit(this.targetListCategories);
  }

  getCategoryTranslation(type: GroupCategoryType) {
    let translation: string = "";
    switch (type) {
      case GroupCategoryType.USERS:
        translation = this.translateService.instant('content.user');
        break;
      case GroupCategoryType.LOCATIONS:
        translation = this.translateService.instant('content.location');
        break;
      case GroupCategoryType.SCHEDULES:
        translation = this.translateService.instant('content.schedule');
        break
    }
    return translation
  }
}