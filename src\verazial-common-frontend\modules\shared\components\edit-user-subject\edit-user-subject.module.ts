import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { ButtonModule } from 'primeng/button';
import { PasswordModule } from 'primeng/password';
import { TranslateModule } from '@ngx-translate/core';
import { MultiSelectModule } from 'primeng/multiselect';
import { DialogModule } from 'primeng/dialog';
import { CheckboxModule } from 'primeng/checkbox';
import { CameraDialogModule } from '../camera-dialog/camera-dialog.module';
import { ProfilePicModule } from '../profile-pic/profile-pic.module';
import { SpeedDialModule } from 'primeng/speeddial';
import { ScrollPanelModule } from 'primeng/scrollpanel';
import { CameraModule } from '../camera/camera.module';
import { EditUserSubjectComponent } from './edit-user-subject/edit-user-subject.component';
import { UploadFilesModule } from '../upload-files/upload-files.module';
import { NewSelectOptionModule } from '../new-select-option/new-select-option.module';
import { DividerModule } from 'primeng/divider';
import { InactivityMonitorModule } from '../inactivity-monitor/inactivity-monitor.module';

@NgModule({
  declarations: [
    EditUserSubjectComponent
  ],
  imports: [
    /* Angular */
    CommonModule,
    /* Translate */
    TranslateModule,
    /* Forms */
    ReactiveFormsModule,
    FormsModule,
    /* PrimeNG */
    InputTextModule,
    DropdownModule,
    CalendarModule,
    ButtonModule,
    PasswordModule,
    MultiSelectModule,
    DialogModule,
    CheckboxModule,
    SpeedDialModule,
    ScrollPanelModule,
    DividerModule,
    /* Custom */
    ProfilePicModule,
    CameraDialogModule,
    CameraModule,
    UploadFilesModule,
    NewSelectOptionModule,
    InactivityMonitorModule,
  ],
  exports: [
    EditUserSubjectComponent
  ]
})
export class EditUserSubjectModule { }
