<ng-template #loadingSpinner>
    <div class="flex justify-content-center align-content-center w-full h-full mt-4">
        <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)"
            animationDuration=".5s" ariaLabel="loading" [ngClass]="{'spinner': isLoading}"></p-progressSpinner>
    </div>
</ng-template>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<div *ngIf="!isLoading else loadingSpinner" class="subcontainer">
    <div class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ 'headers.locations' | translate}}</label>
                </div>
                <div class="tableNumSelectedRowsText flex flex-row align-items-center px-3 border-x-1 border-300">
                    @if(selectedLocations.length > 0){
                    <div>
                        {{ selectedLocations.length + ' ' + ('content.selected' | translate) }}
                    </div>
                    <button pButton [disabled]="!canReadAndWrite && !readOnly || selectedLocations.length > 1"
                        icon="pi pi-{{ readOnly || !userIsVerified ? 'eye' : 'pencil' }}" [text]="true" class="ml-3"
                        style="padding: 0; width: 1.5rem;" (click)="editLocation()"></button>
                    <button pButton [disabled]="!canReadAndWrite || !userIsVerified" icon="pi pi-trash" [text]="true"
                        class="ml-2" style="padding: 0; width: 1.5rem;" (click)="deleteLocation()"></button>
                    }
                </div>
            </div>
            <div class="flex flex-row flex-wrap justify-content-center gap-4 align-items-center">
                <div class="add-action-main-full">
                    <p-button ngClass="add-action-main-full" [disabled]="!(canReadAndWrite && userIsVerified)"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'prisons_tab.new_location' | translate }}" icon="pi pi-plus" iconPos="right"
                        [rounded]="true" (onClick)="showNewLocDialogFunc()"></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button [disabled]="!(canReadAndWrite && userIsVerified)"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        icon="pi pi-plus" [rounded]="true" (onClick)="showNewLocDialogFunc()"></p-button>
                </div>
            </div>
        </div>
        <div></div>
        <p-table #dt [value]="listOfLocations" (onFilter)="onFilter($event, dt)"
            [(selection)]="selectedLocations" (selectionChange)="onSubjectLocationSelectionChange($event)"
            dataKey="id" [rowHover]="true" [paginator]="true" [rows]="10" [rowsPerPageOptions]="[5, 10, 20]"
            [scrollable]="true" scrollDirection="horizontal" [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table" [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="['entryDate', 'locationId', 'regime', 'clasification', 'pending', 'actualLocation', 'exitDate', 'comments']"
            [sortField]="'entryDate'" [sortOrder]="-1">
            <ng-template pTemplate="header">
                <tr>
                    <th style="width: 4rem"></th>
                    <th class="fixed-column" pSortableColumn="entryDate"> {{ 'prisons_tab.init_date' | translate }}
                        <p-sortIcon field="entryDate"></p-sortIcon>
                    </th>
                    <th class="fixed-column" pSortableColumn="locationId">{{ 'prisons_tab.location' | translate }}
                        <p-sortIcon field="locationId"></p-sortIcon>
                    </th>
                    <th class="fixed-column" pSortableColumn="regime">{{ 'prisons_tab.regime' | translate }} <p-sortIcon
                            field="regime"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="classification">{{ 'prisons_tab.clasification' | translate
                        }} <p-sortIcon field="classification"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="pending">{{ 'prisons_tab.pending' | translate }}
                        <p-sortIcon field="pending"></p-sortIcon>
                    </th>
                    <th class="fixed-column" pSortableColumn="actualLocation">{{ 'prisons_tab.actual_location' | translate }}
                        <p-sortIcon field="actualLocation"></p-sortIcon>
                    </th>
                    <th class="fixed-column" pSortableColumn="exitDate"> {{ 'prisons_tab.end_date' | translate }}
                        <p-sortIcon field="exitDate"></p-sortIcon>
                    </th>
                    <th class="fixed-column" pSortableColumn="comments"> {{ 'content.comments' | translate }}
                        <p-sortIcon field="comments"></p-sortIcon>
                    </th>
                    <th class="fixed-column" alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th style="width: 4rem">
                        <p-tableHeaderCheckbox />
                    </th>
                    <th>
                        <p-columnFilter type="date" field="entryDate" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroup">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'entryDate')"
                                    (onInput)="applyDateRangeFilter(dt, 'entryDate')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'entryDate')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="locationId" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">

                                <p-treeSelect appendTo="body"
                                [filter]="true" [filterInputAutoFocus]="true"
                                    class="md:w-20rem w-full"
                                    containerStyleClass="w-full"
                                    [options]="lLocationOptions"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    (onNodeSelect)="filter($event.node.key)"
                                    (onClear)="filter(null)"
                                >
                                </p-treeSelect>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="regime" matchMode="equals" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown
                                    appendTo="body"
                                    [ngModel]="value"
                                    [options]="lRegimeOptions"
                                    (onChange)="filter($event.value?.key)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="label">
                                    <ng-template pTemplate="selectedItem">
                                        {{ value }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.key }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="classification" matchMode="equals" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown
                                    appendTo="body"
                                    [ngModel]="value"
                                    [options]="lClassificationOptions"
                                    (onChange)="filter($event.value?.key)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="label">
                                    <ng-template pTemplate="selectedItem">
                                        {{ value }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.key }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="pending" matchMode="equals" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown
                                    appendTo="body"
                                    [ngModel]="value"
                                    [options]="[
                                        { label: 'options.true' | translate, value: true },
                                        { label: 'options.false' | translate, value: false }
                                    ]"
                                    (onChange)="filter($event.value?.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="label">
                                    <ng-template pTemplate="selectedItem">
                                        {{ value !== undefined ? (value ? ('options.true' | translate) : ('options.false' | translate)) : '' }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.label }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="actualLocation" matchMode="equals" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown
                                    appendTo="body"
                                    [ngModel]="value"
                                    [options]="[
                                        { label: 'options.true' | translate, value: true },
                                        { label: 'options.false' | translate, value: false }
                                    ]"
                                    (onChange)="filter($event.value?.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="label">
                                    <ng-template pTemplate="selectedItem">
                                        {{ value !== undefined ? (value ? ('options.true' | translate) : ('options.false' | translate)) : '' }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.label }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>

                    <th>
                        <p-columnFilter type="date" field="exitDate" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroup">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilterExit(dt, 'exitDate')"
                                    (onInput)="applyDateRangeFilterExit(dt, 'exitDate')"
                                    (onClickOutside)="applyDateRangeFilterExit(dt, 'exitDate')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>

                    <th>
                        <p-columnFilter type="text" field="comments" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-subjectLocation let-rowIndex="rowIndex">
                <tr class="p-selectable-row" [pSelectableRow]="subjectLocation" [pSelectableRowIndex]="rowIndex">
                    <td>
                        <p-tableCheckbox [value]="subjectLocation"></p-tableCheckbox>
                    </td>
                    @if(isValidDate(subjectLocation.entryDate)){
                        <td (click)="editLocation(subjectLocation)" showDelay="1000" pTooltip="{{subjectLocation.entryDate.toISOString() | date:('dateTimeFormat' | translate)}}"
                        tooltipPosition="top" class="ellipsis-cell">{{ subjectLocation.entryDate.toISOString() | date:('dateTimeFormat' | translate)}}</td>
                    }@else{
                        <td (click)="editLocation(subjectLocation)" showDelay="1000" pTooltip=""
                        tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <td (click)="editLocation(subjectLocation)" showDelay="1000"
                        pTooltip="{{newLocationsService.getNameAndTree(subjectLocation.locationId, lLocationOptions)}}" tooltipPosition="top"
                        class="ellipsis-cell">{{ newLocationsService.getNameAndTree(subjectLocation.locationId, lLocationOptions) }}</td>
                    <td (click)="editLocation(subjectLocation)" showDelay="1000"
                        pTooltip="{{subjectLocation.regime}}" tooltipPosition="top" class="ellipsis-cell">{{
                        subjectLocation.regime }}</td>
                    <td (click)="editLocation(subjectLocation)" showDelay="1000"
                        pTooltip="{{ subjectLocation.classification}}" tooltipPosition="top" class="ellipsis-cell">{{
                        subjectLocation.classification }}</td>
                    <td (click)="editLocation(subjectLocation)" showDelay="1000"
                        pTooltip="{{( subjectLocation.pending ? 'options.true' : 'options.false' ) | translate}}"
                        tooltipPosition="top" class="ellipsis-cell">{{ ( subjectLocation.pending ? 'options.true' :
                        'options.false' ) | translate }}</td>
                    <td (click)="editLocation(subjectLocation)" showDelay="1000"
                        pTooltip="{{( subjectLocation.actualLocation? 'options.true' : 'options.false' ) | translate}}"
                        tooltipPosition="top" class="ellipsis-cell">{{ ( subjectLocation.actualLocation ? 'options.true' :
                        'options.false' ) | translate }}</td>
                    @if(isValidDate(subjectLocation.exitDate)){
                        <td (click)="editLocation(subjectLocation)" showDelay="1000" pTooltip="{{subjectLocation.exitDate.toISOString() | date:('dateTimeFormat' | translate)}}"
                        tooltipPosition="top" class="ellipsis-cell">{{ subjectLocation.exitDate.toISOString() | date:('dateTimeFormat' | translate)}}</td>
                    }@else{
                        <td (click)="editLocation(subjectLocation)" showDelay="1000" pTooltip=""
                        tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <td (click)="editLocation(subjectLocation)" showDelay="1000"
                        pTooltip="{{subjectLocation.comments}}" tooltipPosition="top" class="ellipsis-cell">{{
                        subjectLocation.comments }}</td>
                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                        <div class="flex flex-row">
                            <button pButton pRipple [disabled]="!canReadAndWrite && !readOnly"
                                icon="pi pi-{{ readOnly || !userIsVerified ? 'eye' : 'pencil' }}" [text]="true"
                                class="mr-2" style="padding: 0; width: 1.5rem;"
                                (click)="editLocation(subjectLocation)"></button>
                            <button pButton pRipple
                                [disabled]="!canReadAndWrite || !userIsVerified"
                                icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;"
                                (click)="deleteLocation(subjectLocation)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>


        <p-dialog [(visible)]="showNewLocDialog" styleClass="p-fluid" [modal]="true" [style]="{'width': '40vw'}"
            (onHide)="onCancelDialog()" (onShow)="trackDataChange()">
            <ng-template pTemplate="header">
                <div>
                    <label for="" [style]="{'color':'#204887', 'font-weight':'700', 'font-size':'20px'}">
                        {{ 'prisons_tab.new_location' | translate }}
                    </label>
                </div>
            </ng-template>
            <ng-template pTemplate="content">
                <div>
                    <div class="flex justify-content-end pb-3 requiredFieldsLabel">
                        {{ 'content.requiredFields' | translate }} <span class="requiredStar">*</span>
                    </div>
                    <form [formGroup]="form">
                        <div class="grid form-fields">

                            <div class="lg:col-12 sm:col-12 width100" style=" padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="locationId">{{ 'prisons_tab.location' | translate
                                        }} <span *ngIf="isRequiredField('locationId')"
                                            class="requiredStar">*</span></label>

                                    <p-treeSelect formControlName="locationId"
                                        (ngModelChange)="trackDataChange()" [options]="lLocationOptions"
                                        appendTo="body" optionLabel="value" placeholder="{{ 'content.select' | translate }}"
                                        [filter]="true" [filterInputAutoFocus]="true" [virtualScroll]="true" [virtualScrollItemSize]="46" [virtualScrollOptions]="{scrollHeight: '200px'}" />
                                    <small *ngIf="!isValid('locationId') && form.controls['locationId'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <div class="lg:col-6 sm:col-12 width100 flex flex-column justify-content-center"
                                style="padding-bottom: 0rem; padding-top: 0rem;">
                                <label class="label-form" for="actualLocation">{{'prisons_tab.actual_location' |
                                    translate}} <span *ngIf="isRequiredField('actualLocation')"
                                            class="requiredStar">*</span></label>
                                <div
                                    class="flex flex-row gap-3 justify-content-center align-items-center align-content-center">
                                    <p-inputSwitch formControlName="actualLocation" [binary]="true"
                                        (ngModelChange)="trackDataChangeActualLocation($event)" [(ngModel)]="location.actualLocation"></p-inputSwitch>
                                    <small *ngIf="!isValid('actualLocation') && form.controls['actualLocation'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <div class="lg:col-6 sm:col-12 width100 flex flex-column justify-content-center"
                                style="padding-bottom: 0rem; padding-top: 0rem;">
                                <label class="label-form" for="pending">{{'prisons_tab.pending' |
                                    translate}} <span *ngIf="isRequiredField('pending')"
                                            class="requiredStar">*</span></label>
                                <div
                                    class="flex flex-row gap-3 justify-content-center align-items-center align-content-center">
                                    <p-inputSwitch formControlName="pending" [binary]="true"
                                        (ngModelChange)="trackDataChangePending($event)" [(ngModel)]="location.pending"></p-inputSwitch>
                                    <small *ngIf="!isValid('pending') && form.controls['pending'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="entryDate">{{ 'prisons_tab.init_date' | translate }}
                                        <span *ngIf="isRequiredField('entryDate')" class="requiredStar">*</span></label>
                                        <p-calendar appendTo="body" formControlName="entryDate"
                                        (ngModelChange)="trackDataChange()" [iconDisplay]="'input'" [showIcon]="true"
                                         inputId="icondisplay"
                                        dateFormat="{{ 'dateFormat' | translate }}" today="{{ 'today' | translate }}" [showButtonBar]="true"
                                        [showTime]="true" [hourFormat]="'hourFormat' | translate" />
                                    <small *ngIf="!isValid('entryDate') && form.controls['entryDate'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="exitDate">{{ 'prisons_tab.end_date' | translate }}
                                        <span *ngIf="isRequiredField('exitDate')" class="requiredStar">*</span></label>
                                        <p-calendar appendTo="body" formControlName="exitDate"
                                        (ngModelChange)="trackDataChange()" [iconDisplay]="'input'" [showIcon]="true"
                                         inputId="icondisplay"
                                        dateFormat="{{ 'dateFormat' | translate }}" today="{{ 'today' | translate }}" [showButtonBar]="true"
                                        [showTime]="true" [hourFormat]="'hourFormat' | translate" />
                                    <small *ngIf="!isValid('exitDate') && form.controls['exitDate'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="regime">{{ 'prisons_tab.regime' | translate
                                        }} <span *ngIf="isRequiredField('regime')"
                                            class="requiredStar">*</span></label>
                                    <p-dropdown formControlName="regime"
                                        (ngModelChange)="trackDataChange()" appendTo="body"
                                        [options]="lRegimeOptions" optionLabel="value"
                                        placeholder="{{ 'content.select' | translate }}" />
                                    <small *ngIf="!isValid('regime') && form.controls['regime'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>

                            <div class="lg:col-6 sm:col-12 width100" style="padding-bottom: 0rem; padding-top: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="classification">{{ 'prisons_tab.clasification' | translate
                                        }} <span *ngIf="isRequiredField('classification')"
                                            class="requiredStar">*</span></label>
                                    <p-dropdown formControlName="classification"
                                        (ngModelChange)="trackDataChange()" appendTo="body"
                                        [options]="lClassificationOptions" optionLabel="value"
                                        placeholder="{{ 'content.select' | translate }}" />
                                    <small *ngIf="!isValid('classification') && form.controls['classification'].touched"
                                        [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate
                                        }}</small>
                                </div>
                            </div>



                            <div class="lg:col-12 sm:col-12 width100" style="padding-bottom: 0rem;">
                                <div class="pb-1">
                                    <label class="label-form" for="comments">{{ 'content.comments' | translate }} <span
                                            *ngIf="isRequiredField('comments')" class="requiredStar">*</span></label>
                                    <input type="text" pInputText formControlName="comments" (ngModelChange)="trackDataChange()" />
                                    <small *ngIf="!isValid('comments') && form.controls['comments'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                </div>
                            </div>


                        </div>
                    </form>
                </div>
            </ng-template>
            <ng-template pTemplate="footer">
                <div class="flex flex-row gap-1 mr-3 justify-content-end">
                    <p-button *ngIf="canReadAndWrite && userIsVerified"
                        [disabled]="!(canReadAndWrite && userIsVerified) || isDisableSaveButton"
                        [style]="{ 'pointer-events': 'auto', 'width': '100px', 'height': '36px', 'color': '#FFFFFF', 'border': 'none', 'background': '#009BA9', 'font-family': 'Open Sans', 'font-size': '14px' }"
                        label="{{ 'save'| translate }}" (onClick)="saveLocation()"></p-button>
                    <p-button
                        [style]="{'width':'100px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#64748B', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ (canReadAndWrite && userIsVerified ? 'cancel' : 'close')| translate }}"
                        (onClick)="onCancelDialog()"></p-button>
                </div>
            </ng-template>
        </p-dialog>
    </div>
</div>