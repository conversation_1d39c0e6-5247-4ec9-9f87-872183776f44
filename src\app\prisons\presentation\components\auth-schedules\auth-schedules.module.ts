import { NgModule } from "@angular/core";
import { AuthSchedulesListComponent } from "./auth-schedules-list/auth-schedules-list.component";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { ProgressSpinnerModule } from "primeng/progressspinner";
import { DialogModule } from "primeng/dialog";
import { ButtonModule } from "primeng/button";
import { TableModule } from "primeng/table";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { InputTextareaModule } from "primeng/inputtextarea";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { DropdownModule } from "primeng/dropdown";
import { InputSwitchModule } from "primeng/inputswitch";
import { CalendarModule } from "primeng/calendar";
import { AccordionModule } from "primeng/accordion";
import { ScrollPanelModule } from "primeng/scrollpanel";
import { CarouselModule } from "primeng/carousel";
import { FloatLabelModule } from "primeng/floatlabel";
import { StepsModule } from "primeng/steps";
import { ToastModule } from "primeng/toast";
import { SelectButtonModule } from "primeng/selectbutton";
import { TreeSelectModule } from 'primeng/treeselect';
import { LoadingSpinnerModule } from "src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module";
import { AuthScheduleEditComponent } from "./auth-schedule-edit/auth-schedule-edit.component";
import { EmptyModule } from "src/verazial-common-frontend/modules/shared/components/empty/empty.module";
import { MultiSelectModule } from "primeng/multiselect";
import { InactivityMonitorModule } from "src/verazial-common-frontend/modules/shared/components/inactivity-monitor/inactivity-monitor.module";

@NgModule({
    declarations: [
      AuthSchedulesListComponent,
        AuthScheduleEditComponent,
    ],
    imports: [
      /* Angular Modules */
      CommonModule,
      /* Forms */
      FormsModule,
      ReactiveFormsModule,
      /* Translate */
      TranslateModule,
      /* PrimeNG Modules */
      ProgressSpinnerModule,
      DialogModule,
      ButtonModule,
      TableModule,
      IconFieldModule,
      InputIconModule,
      InputTextModule,
      InputTextareaModule,
      ConfirmDialogModule,
      DropdownModule,
      InputSwitchModule,
      CalendarModule,
      AccordionModule,
      ScrollPanelModule,
      CarouselModule,
      FloatLabelModule,
      StepsModule,
      ToastModule,
      SelectButtonModule,
      TreeSelectModule,
      MultiSelectModule,
      /* Custom Modules */
      LoadingSpinnerModule,
      EmptyModule,
      InactivityMonitorModule,
    ],
    exports: [
      AuthSchedulesListComponent,
      AuthScheduleEditComponent,
    ]
  })
  export class AuthSchedulesModule { }