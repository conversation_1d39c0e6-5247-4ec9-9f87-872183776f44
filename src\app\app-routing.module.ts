import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthPageComponent } from '../verazial-common-frontend/modules/auth/presentation/pages/auth-page/auth-page/auth-page.component';
import { AppLayoutComponent } from './layout/app.layout.component';
import { AuthGuard } from 'src/verazial-common-frontend/core/guards/auth.guard';
import { NavigationGuard } from 'src/verazial-common-frontend/core/guards/navigation.guard';


const routes: Routes = [
  { path: '', component: AuthPageComponent },
  { path: 'password-recovery/:recoveryToken', component: AuthPageComponent },
  // { path: '', component: BiometricLoginPageComponent },
  { path: 'auth', loadChildren: () => import('../verazial-common-frontend/modules/auth/presentation/pages/auth-page/auth-page.module').then(m => m.AuthPageModule), canActivate: [AuthGuard]},
  {
    path: '',
    component: AppLayoutComponent,
    children: [
      {path: 'home',
      loadChildren: () => import('./home/<USER>/pages/home-page/home-page.module').then(m => m.HomePageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'process/flow',
      loadChildren: () => import('./flows/presentation/pages/flows-page/flows-page.module').then(m => m.FlowsPageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'process/categories',
      loadChildren: () => import('./groups-categories/presentation/pages/group-page/group-page.module').then(m => m.GroupPageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'process/assignments',
      loadChildren: () => import('./group/presentation/pages/admin-group/admin-group.module').then(m => m.AdminGroupModule), canActivate: [AuthGuard, NavigationGuard]},
      /*{path: 'actions/time-records',
      loadChildren: () => import('./lists/presentation/pages/lists-page.module').then(m => m.ListsPageModule), canActivate: [AuthGuard, NavigationGuard]},*/
      {path: 'tenants',
      loadChildren: () => import('./tenant/presentation/pages/tenant-page/tenant-page.module').then(m => m.TenantPageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'roles',
      loadChildren: () => import('./role/presentation/pages/role-page/role-page.module').then(m => m.RolePageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'general',
      loadChildren: () => import('./user-subject/presentation/pages/user-subject-page.module').then(m => m.UserSubjectPageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'pass/app-flows',
      loadChildren: () => import('./application-flow/presentation/pages/application-flow-page/application-flow-page.module').then(m => m.ApplicationFlowPageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'pass/data-sources',
      loadChildren: () => import('./data-sources/presentation/pages/data-source-page/data-source-page.module').then(m => m.DataSourcePageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'pass/applications',
      loadChildren: () => import('./applications/presentation/pages/application-pages.module').then(m => m.ApplicationPagesModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'admin-locations',
      loadChildren: () => import('./locations/presentation/pages/locations-page/locations-page.module').then(m => m.LocationsPageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'reports/verification',
      loadChildren: () => import('./reports/presentation/pages/verification-page/verification-page.module').then(m => m.VerificationPageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'reports/identification',
      loadChildren: () => import('./reports/presentation/pages/identification-page/identification-page.module').then(m => m.IdentificationPageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'reports/performance',
      loadChildren: () => import('./reports/presentation/pages/performance-page/performance-page.module').then(m => m.PerformancePageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'reports/general',
      loadChildren: () => import('./reports/presentation/pages/main-page/main-page.module').then(m => m.MainPageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'reports/users',
      loadChildren: () => import('./reports/presentation/pages/user-page/user-page.module').then(m => m.UserPageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'reports/subjects',
      loadChildren: () => import('./reports/presentation/pages/subject-page/subject-page.module').then(m => m.SubjectPageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'reports/actions',
      loadChildren: () => import('./reports/presentation/pages/action-page/action-page.module').then(m => m.ActionPageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'reports/audit-trail',
      loadChildren: () => import('./reports/presentation/pages/audit-page/audit-page.module').then(m => m.AuditPageModule), canActivate: [AuthGuard, NavigationGuard]},
      {path: 'prisons',
      loadChildren: () => import('./prisons/presentation/pages/prisons-page.module').then(m => m.PrisonsPageModule), canActivate: [AuthGuard, NavigationGuard]},
    ]
  },
  {path: 'error',
    loadChildren: () => import('../verazial-common-frontend/modules/error-pages/presentation/pages/error-pages.module').then(m => m.ErrorPagesModule)},
  {path: '**',
    pathMatch: 'full',
    redirectTo: '/error/404'}
];

@NgModule({
  imports: [RouterModule.forRoot(routes, {
    initialNavigation: 'enabledBlocking'
})],
  exports: [RouterModule]
})
export class AppRoutingModule { }
