import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TaskFlowEntity } from 'src/verazial-common-frontend/core/general/flow/domain/entity/task-flow.entity';
import { GetAllPublishedTaskFlowsUseCase } from 'src/verazial-common-frontend/core/general/flow/domain/use-cases/task-flow/get-all-published-task-flows.use-case';
import { AssignmentElementEntity } from 'src/verazial-common-frontend/core/general/assignment//group/domain/entity/assignment-elements.entity';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { AuditTrailService } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'app-flow-assignment',
  templateUrl: './flow-assignment.component.html',
  styleUrl: './flow-assignment.component.css'
})
export class FlowAssignmentComponent implements OnInit {
  @Input() inputData: AssignmentElementEntity[] = [];
  @Output() listFlows = new EventEmitter<TaskFlowEntity[]>();

  sourceListFlows: TaskFlowEntity[] = [];
  targetListFlows: TaskFlowEntity[] = [];

  ngOnInit(): void {
    this.getAllPublishedFlows();
    /*if(this.inputData.length > 0){
      this.targetListFlows = this.inputData;
    }*/
  }

  constructor(
    private getAllPublishedTaskFlowsUseCase: GetAllPublishedTaskFlowsUseCase,
    private loggerService: ConsoleLoggerService,
    private auditTrailService: AuditTrailService,
    private localStorageService: LocalStorageService,
  ){}

  getAllPublishedFlows(){
    this.getAllPublishedTaskFlowsUseCase.execute().then(
      (data) => {
        this.sourceListFlows = data;
        if(this.inputData.length>0){
          this.targetListFlows = this.sourceListFlows.filter((s)=> this.inputData.some(element => s.id == element.elementId));
          this.targetListFlows.map((t)=>{
            this.sourceListFlows = this.sourceListFlows.filter((s)=>t.id!=s.id)
          });
        }
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_PUBLISHED_TASK_FLOWS, 0, 'ERROR', '', at_attributes);
      })
  }

  targetTrackChanges(){
    this.listFlows.emit(this.targetListFlows);
  }
  
}
