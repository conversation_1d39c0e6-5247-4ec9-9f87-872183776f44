import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

// PrimeNG Modules
import { ButtonModule } from 'primeng/button';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';

// Verazial Common Modules
import { InactivityMonitorModule } from 'src/verazial-common-frontend/modules/shared/components/inactivity-monitor/inactivity-monitor.module';

// Component
import { ExampleAuditTrailWithInactivityComponent } from './example-audit-trail-with-inactivity.component';

@NgModule({
  declarations: [
    ExampleAuditTrailWithInactivityComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    
    // PrimeNG
    ButtonModule,
    ToastModule,
    ConfirmDialogModule,
    
    // Verazial Common
    InactivityMonitorModule
  ],
  exports: [
    ExampleAuditTrailWithInactivityComponent
  ]
})
export class ExampleAuditTrailWithInactivityModule { }
