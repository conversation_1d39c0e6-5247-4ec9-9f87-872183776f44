<div class="admin-groups card mt-4">
    <p-pickList
    [disabled]="!canReadAndWrite"
    [source]="sourceListCategories"
    [target]="targetListCategories"
    sourceHeader="{{ 'content.available' | translate }}"
    targetHeader="{{ 'content.selected' | translate }}"
    [dragdrop]="true"
    [showSourceControls]="false"
    [showTargetControls]="false"
    filterBy="name"
    [responsive]="true"
    [sourceStyle]="{ height: '20rem', width: '20rem' }"
    [targetStyle]="{ height: '20rem', width: '20rem' }"
    (onMoveToSource)="targetTrackChanges()"
    (onMoveToTarget)="targetTrackChangesToTarget($event)"
    (onMoveAllToSource)="targetTrackChanges()"
    (onMoveAllToTarget)="targetTrackChangesAllToTarget($event)"
    breakpoint="1400px">
    <ng-template let-category pTemplate="item">
        <div class="flex flex-wrap p-2 align-items-center gap-3">
            <div class="flex-1 flex flex-column gap-2">
                <span class="font-bold">{{ category.name }}</span>
                <div class="flex align-items-center gap-2 ">
                    @if (category.type==categoryTypes.USERS) {
                        <i class="pi pi-users"></i>
                    }@else if (category.type==categoryTypes.LOCATIONS) {
                        <i class="pi pi-map-marker"></i>
                    }@else if (category.type==categoryTypes.SCHEDULES) {
                        <i class="pi pi-clock"></i>
                    }@else {
                        <i class="pi pi-cog"></i>
                    }
                    <span class="text-xs">
                        {{getCategoryTranslation(category.type)}}
                    </span>
                </div>
            </div>
        </div>
    </ng-template>
    </p-pickList>
</div>