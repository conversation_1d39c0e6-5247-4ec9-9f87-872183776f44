import { Component, EventEmitter, HostListener, Input, OnChanges, Output, SimpleChanges } from "@angular/core";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";

@Component({
    selector: 'app-inactivity-monitor',
    templateUrl: './inactivity-monitor.component.html',
    styleUrl: './inactivity-monitor.component.css'
})
export class InactivityMonitorComponent implements OnChanges {

    // Inputs
    // Interval to start checking inactivity
    @Input() intervalStart: number = 5000;
    // Interval to check inactivity
    @Input() interval: number = 1000;
    // Inactivity timeout in milliseconds
    @Input() inactivityTimeoutLimit: number = 0;
    // Flag to start checking inactivity
    @Input() startChecking: boolean = false;

    // Outputs
    @Output() expired = new EventEmitter<void>();

    // Elapsed time in milliseconds
    elapsedTime = 0;
    // Flag to check if the user is active
    isActivity = false;
    // Interval to start checking inactivity
    countRefreshStart: any;
    // Interval to check if the user is inactive
    isUserActivity: any;

    constructor(
        private localStorageService: LocalStorageService,
        private loggerService: ConsoleLoggerService,
    ) { }

    ngOnChanges(changes: SimpleChanges): void {
        if (this.startChecking && changes['startChecking'] && changes['startChecking'].currentValue) {
            this.loggerService.debug("Inactivity monitor started");
            this.startCheckingInactivity();
        }
        else if (!this.startChecking && changes['startChecking'] && changes['startChecking'].previousValue) {
            this.loggerService.debug("Inactivity monitor stopped");
            this.stopCheckingInactivity();
        }
    }

    startCheckingInactivity() {
        this.isActivity = true;
        clearInterval(this.isUserActivity);
        clearTimeout(this.countRefreshStart);
        if (this.inactivityTimeoutLimit > 0 && this.startChecking) {
            this.countRefreshStart = setTimeout(() => {
                this.loggerService.debug("Inactivity countdown started");
                this.elapsedTime = 0;
                this.countDown();
            }, this.intervalStart);
        }
    }

    stopCheckingInactivity() {
        this.isActivity = false;
        clearInterval(this.isUserActivity);
        clearTimeout(this.countRefreshStart);
    }

    countDown() {
        this.isActivity = true;
        clearInterval(this.isUserActivity);

        if (this.inactivityTimeoutLimit > 0) {
            this.isUserActivity = setInterval(() => {
                if (this.startChecking && this.inactivityTimeoutLimit > 0 && !this.isEmpty(this.localStorageService.getUser())) {
                    this.isActivity = false;

                    this.elapsedTime = this.elapsedTime + this.interval;
                    // console.log("Elapsed time: " + this.elapsedTime);
                    // console.log("Lock time: " + this.lockTime);
                    if (this.elapsedTime == this.inactivityTimeoutLimit) {
                        this.loggerService.debug("Inactivity timeout reached");
                        this.stopCheckingInactivity();
                        this.expired.emit();
                    }
                }
            }, this.interval);
        }
    }

    isEmpty(obj: Object) {
        if (obj == null) {
            return true;
        }
        return Object.keys(obj).length === 0;
    }

    @HostListener('document:mousemove', ['$event']) onMosusemove(e: MouseEvent) {
        // console.log('Mouse move event detected');
        this.startCheckingInactivity();
    }

    @HostListener('document:keydown', ['$event']) onKeydown(e: KeyboardEvent) {
        // console.log('Keydown event detected');
        this.startCheckingInactivity();
    }

    @HostListener('document:scroll', ['$event']) onScroll(e: Event) {
        // console.log('Scroll event detected');
        this.startCheckingInactivity();
    }

    @HostListener('document:click', ['$event']) onClick(e: MouseEvent) {
        // console.log('Click event detected');
        this.startCheckingInactivity();
    }

    @HostListener('document:touchstart', ['$event']) onTouchStart(e: TouchEvent) {
        // console.log('Touch start event detected');
        this.startCheckingInactivity();
    }

    @HostListener('document:touchmove', ['$event']) onTouchMove(e: TouchEvent) {
        // console.log('Touch move event detected');
        this.startCheckingInactivity();
    }

    @HostListener('document:touchend', ['$event']) onTouchEnd(e: TouchEvent) {
        // console.log('Touch end event detected');
        this.startCheckingInactivity();
    }
}