import { Component, EventEmitter, Input, On<PERSON><PERSON><PERSON>, OnDestroy, OnInit, Output, SimpleChanges, ViewChild, ViewContainerRef } from "@angular/core";
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { Table } from "jspdf-autotable";
import { ConfirmationService, FilterService, MessageService } from "primeng/api";
import { CustomFieldModel } from "src/verazial-common-frontend/core/general/manager/common/models/custom-field.model";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { JudicialFileEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/judicial-files.entity";
import { GetJudicialFilesBySubjectIdUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/judicial/get-judicial-files-by-subject-id.use-case";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { AccessIdentifier } from "src/verazial-common-frontend/core/models/access-identifier.enum";
import { CheckPermissionsService } from "src/verazial-common-frontend/core/services/check-permissions-service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { ValidatorService } from "src/verazial-common-frontend/modules/shared/services/validator.service";
import { CustomFieldTypes } from "src/verazial-common-frontend/core/general/manager/common/models/custom-field-type.enum";
import { environment } from "src/environments/environment";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { TranslateService } from "@ngx-translate/core";
import { SaveJudicialFileUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/judicial/save-judicial-file.use-case";
import { UpdateJudicialFileUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/judicial/update-judicial-file.use-case";
import { DeleteJudicialFileByIdUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/judicial/delete-judicial-file-by-id.use-case";
import { DynamicFormAttributes, DynamicFormComponent } from "src/app/user-subject/presentation/components/dynamic-form/dynamic-form/dynamic-form.component";
import { DetailsDataEntity } from "src/verazial-common-frontend/core/general/common/entity/details-data.entity";
import { TranslationGroup, TranslationModel } from "src/verazial-common-frontend/core/general/manager/common/models/translation.model";

@Component({
    selector: 'app-judicial-files-list',
    templateUrl: './judicial-files-list.component.html',
    styleUrl: './judicial-files-list.component.css'
})
export class JudicialFilesListComponent implements OnInit, OnChanges, OnDestroy {

    // Inputs
    @Input() userSubject?: SubjectEntity;
    @Input() userIsVerified: boolean = false;
    // Outputs
    @Output() contentModified = new EventEmitter<boolean>();

    isLoading: boolean = false;
    modified: boolean = false;

    confirmDialogTimeoutLimit: number = 0;
    startCheckingInactivity: boolean = false;

    canReadAndWrite: boolean = false;
    readOnly: boolean = false;
    access_identifier: string = AccessIdentifier.PRISONS_JUDICIAL_FILES;
    isDisabledSaveButton: boolean = true;

    listOfJudicialFiles: JudicialFileEntity[] = [];
    selectedJudicialFiles: JudicialFileEntity[] = [];

    showJudicialFileDialog: boolean = false;

    isNew: boolean = true;
    judicialFile?: JudicialFileEntity;
    public form: FormGroup = this.fb.group({
        judicialFile: ['', [Validators.required]],
        comments: [''],
    });

    filteredValues: any[] = [];
    // Date Range Filter
    formGroup: FormGroup = new FormGroup({
        date: new FormControl<Date[] | null>(null)
    });
    dateFilterValues = {
        startDate: null,
        endDate: null
    };
    rangeDates: Date[] | null = null;

    // Settings
    managerSettings?: GeneralSettings;

    // Dynamic Form
    @ViewChild('dynamicFormContent', { read: ViewContainerRef, static: true }) dynamicFormContent: ViewContainerRef;
    formAttributes: DynamicFormAttributes[] = [];
    showDynamicForm: boolean = true;
    componentRef: any;
    formModified: boolean = false;

    constructor(
            private filterService: FilterService,
            private checkPermissions: CheckPermissionsService,
            private validatorService: ValidatorService,
            private getJudicialFilesBySubjectIdUseCase: GetJudicialFilesBySubjectIdUseCase,
            private fb: FormBuilder,
            private localStorageService: LocalStorageService,
            private loggerService: ConsoleLoggerService,
            private auditTrailService: AuditTrailService,
            private messageService: MessageService,
            private translateService: TranslateService,
            private saveJudicialFileUseCase: SaveJudicialFileUseCase,
            private updateJudicialFileUseCase: UpdateJudicialFileUseCase,
            private confirmationService: ConfirmationService,
            private deleteJudicialFileByIdUseCase: DeleteJudicialFileByIdUseCase,
    ) {
        this.dynamicFormContent = {} as ViewContainerRef;
        this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
            if (!filter || (!filter.startDate && !filter.endDate)) {
                return true; // If no filter, show all
            }
            const dateValue = new Date(value).getTime();
            const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
            const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
            if (startDate && endDate) {
                return dateValue >= startDate && dateValue <= endDate;
            } else if (startDate) {
                return dateValue >= startDate;
            } else if (endDate) {
                return dateValue <= endDate;
            }
            return false;
        });
    }

    ngOnInit() {
        this.managerSettings = this.localStorageService.getSessionSettings()!;
        this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
        this.loadData();
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['userSubject'] && changes['userSubject'].currentValue) {
            this.ngOnInit();
        }
    }

    ngOnDestroy() {
        // Clean up the timeout if the component is destroyed
        this.closeConfirmationDialog();
    }

    resetInactivityMonitor() {
        this.startCheckingInactivity = false;
        this.confirmDialogTimeoutLimit = 0;
    }

    loadData() {
        this.isLoading = true;
        this.getJudicialFilesBySubjectIdUseCase.execute({ id: this.userSubject?.id! }).then(
            (data) => {
                this.listOfJudicialFiles = data;
            },
            (e) => {
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_JUDICIAL_FILES_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
            }
        )
        .finally(() => {
            this.isLoading = false;
        });
    }

    onNewJudicialFile() {
        this.isNew = true;
        this.showJudicialFileDialog = true;
        this.judicialFile = new JudicialFileEntity();
        this.judicialFile.data = [];
        this.judicialFile.registrationDate = new Date();
        this.judicialFile.createdAt = new Date();
        this.judicialFile.updatedAt = new Date();
        this.componentRef?.destroy();
        this.buildDynamicForm();
    }

    trackDataChange() {
        if (!this.judicialFile) {
            this.judicialFile = new JudicialFileEntity();
        }
        if (!this.modified) {
            this.updateModified(true);
        }
        this.judicialFile.subjectId = this.userSubject?.id;
        this.judicialFile.judicialFile = this.form.get('judicialFile')?.value;
        this.judicialFile.comments = this.form.get('comments')?.value;
        this.isDisabledSaveButton = !this.form.valid || this.judicialFile?.data?.length == 0 || this.judicialFile.data == null || this.judicialFile.data == undefined;
    }

    editJudicialFile(judicialFile: JudicialFileEntity) {
        this.loggerService.debug(judicialFile);
        if (!judicialFile) {
            if (this.selectedJudicialFiles.length === 1) {
                judicialFile = this.selectedJudicialFiles[0];
            }
            else {
                return;
            }
        }
        this.form.reset();
        this.isNew = false;
        this.judicialFile = {...judicialFile};
        this.form.get('judicialFile')?.setValue(judicialFile.judicialFile);
        this.form.get('comments')?.setValue(judicialFile.comments);
        if (this.readOnly || !this.userIsVerified) {
            this.form.disable();
        }
        else {
            this.form.enable();
        }
        this.showJudicialFileDialog = true;
        this.componentRef?.destroy();
        this.buildDynamicForm();
    }

    deleteJudicialFile(judicialFile?: JudicialFileEntity) {
        let dataToDelete: JudicialFileEntity[] = [];
        if (this.selectedJudicialFiles.length >= 1 && !judicialFile) {
            dataToDelete = this.selectedJudicialFiles;
        } else if (judicialFile) {
            dataToDelete.push(judicialFile);
        }
        this.confirmDelete(dataToDelete);
    }

    confirmDelete(dataToDelete: JudicialFileEntity[]) {
        let message: string = ""
        if (dataToDelete.length == 1) {
            message = `${this.translateService.instant('messages.delete_single_record')}?`;
        } else {
            message = this.translateService.instant('messages.delete_multiple_records') + "<br>(" + dataToDelete.length + ")<br>";
        }
        this.confirmationService.confirm({
            message: message,
            header: this.translateService.instant('messages.delete_confirmation_header'),
            icon: 'pi pi-exclamation-triangle',
            rejectButtonStyleClass: "p-button-text",
            acceptButtonStyleClass: "ng-confirm-button",
            acceptIcon: "none",
            rejectIcon: "none",
            acceptLabel: this.translateService.instant("delete"),
            rejectLabel: this.translateService.instant("no"),
            accept: () => {
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(dataToDelete) },
                ];
                this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.SUBJECT, AuditTrailActions.DEL_JDF, ReasonActionTypeEnum.DELETE, () => { this.onSubmitDelete(dataToDelete); }, at_attributes);
            },
            reject: () => {
                this.resetInactivityMonitor();
            }
        });

        this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
        this.startCheckingInactivity = false;
        setTimeout(() => this.startCheckingInactivity = true, 0);
    }

    async onSubmitDelete(dataToDelete: JudicialFileEntity[]) {
        this.isLoading = true;
        try {
            // Wait for all deletions to complete
            await Promise.all(dataToDelete.map(record => this.deleteJudicialFileById(record)));

            // Success message
            this.messageService.add({
                severity: 'success',
                summary: this.translateService.instant('content.successTitle'),
                detail: this.translateService.instant('messages.success_general'),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });

            // Clear selected items
            this.selectedJudicialFiles = [];
        } catch (error: any) {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('content.errorTitle'),
                detail: `${this.translateService.instant('messages.error_deleting_judicial_file')}: ${error.message}`,
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });

            this.loggerService.error('Error deleting judicial files:');
            this.loggerService.error(error);
        } finally {
            this.isLoading = false;
        }
    }

    deleteJudicialFileById(judicialFile: JudicialFileEntity): Promise<void> {
        return new Promise(async (resolve, reject) => {
            try {
                const data = await this.deleteJudicialFileByIdUseCase.execute({ id: judicialFile.id! });

                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(judicialFile) },
                ];

                if (data.success) {
                    this.listOfJudicialFiles = this.listOfJudicialFiles.filter((b) => b.id !== judicialFile.id);
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_JDF, 0, 'SUCCESS', '', at_attributes);
                    resolve();
                } else {
                    at_attributes.push({ name: AuditTrailFields.ERROR, value: JSON.stringify(data) });
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_JDF, 0, 'ERROR', '', at_attributes);
                    reject(new Error("Failed to delete judicial file"));
                }
            } catch (error) {
                this.loggerService.error(error);

                const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(error) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_JDF, 0, 'ERROR', '', at_attributes);

                reject(error);
            }
        });
    }

    onFilter(event: any, dt: Table) {
        this.filteredValues = event.filteredValue;
        if (!event.filters['registrationDate'].value) {
            this.rangeDates = null;
            this.formGroup.reset();
        }
    }

    onJudicialFilesSelectionChange(event: any) {}

    onCancelDialog() {
        if(this.modified || this.formModified) {
            this.confirmationService.confirm({
                message: this.translateService.instant('messages.exitingWithoutSaving'),
                header: this.translateService.instant('titles.exitingWithoutSaving'),
                icon: 'pi pi-exclamation-triangle',
                acceptIcon: "none",
                acceptLabel: this.translateService.instant('options.true'),
                rejectIcon: "none",
                rejectLabel: this.translateService.instant('options.false'),
                rejectButtonStyleClass: "p-button-text",
                accept: () => {
                    this.resetPage();
                },
                reject: () => {
                    this.resetInactivityMonitor();
                }
            });

            this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
            this.startCheckingInactivity = false;
            setTimeout(() => this.startCheckingInactivity = true, 0);
        }
        else {
            this.resetPage();
        }
    }

    resetPage() {
        this.isNew = true;
        this.showJudicialFileDialog = false;
        this.componentRef?.destroy();
        this.judicialFile = undefined;
        this.form.reset();
        this.updateModified(false);
    }

    saveJudicialFile() {
        if(this.form.valid && this.judicialFile) {
            this.isLoading = true;
            this.loggerService.error(this.judicialFile);
            if(this.isNew) {
                this.saveJudicialFileUseCase.execute({ judicialFile: this.judicialFile}).then(
                    (data) => {
                        const at_attributes: ExtraData[] = [
                            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(data) },
                        ];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_JDF, 0, 'SUCCESS', '', at_attributes);
                        this.updateModified(false);
                        this.onCancelDialog();
                        this.loadData();
                    },
                    (e) => {
                        this.loggerService.error(e);
                        const at_attributes: ExtraData[] = [
                            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.judicialFile) },
                            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) }
                        ];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_JDF, 0, 'ERROR', '', at_attributes);
                    }
                )
                .finally(() => {
                    this.isLoading = false;
                });
            }
            else {
                this.updateJudicialFileUseCase.execute({ judicialFile: this.judicialFile}).then(
                    (data) => {
                        let oldValue = this.listOfJudicialFiles.find((b) => b.id === data?.id);
                        const at_attributes: ExtraData[] = [
                            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(oldValue) },
                            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(data) },
                        ];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_JDF, 0, 'SUCCESS', '', at_attributes);
                        this.updateModified(false);
                        this.onCancelDialog();
                        this.loadData();
                    },
                    (e) => {
                        this.loggerService.error(e);
                        let oldValue = this.listOfJudicialFiles.find((b) => b.id === this.judicialFile?.id);
                        const at_attributes: ExtraData[] = [
                            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(oldValue) },
                            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_JDF, 0, 'ERROR', '', at_attributes);
                    }
                )
                .finally(() => {
                    this.isLoading = false;
                });
            }
        }
    }

    /* Dynamic Form Functions */

    buildDynamicForm() {
        const extBioFields: CustomFieldModel[] = this.managerSettings?.continued1?.prisonsSettings?.judicialFileFields || [];
        if (extBioFields.length != 0) {
            this.loggerService.info('There are Judicial Files Fields to submit');
            this.formAttributes = [];
            // Classify the attributes of the action
            extBioFields.forEach((extBioField: CustomFieldModel) => {
                if (extBioField.type == CustomFieldTypes.INPUT || extBioField.type == CustomFieldTypes.DROPDOWN || extBioField.type == CustomFieldTypes.TOGGLE) {
                    let type: CustomFieldTypes = extBioField.type;
                    let required = false;
                    let maxCharacters = 0;
                    let options: string[] = [];
                    extBioField.fieldData?.forEach((data: any) => {
                        if (data.key.includes('required-checkbox')) {
                            required = data.value == 'true';
                        }
                        if (data.key.includes('options-listbox')) {
                            options = JSON.parse(data.value);
                        }
                        if (data.key.includes('max-characters-textbox')) {
                            maxCharacters = data.value;
                        }
                    });
                    if (type == CustomFieldTypes.INPUT && maxCharacters > (this.managerSettings?.continued1?.inputTextAreaThreshold ?? 25)) {
                        type = CustomFieldTypes.TEXTAREA;
                    }
                    const formAttribute: DynamicFormAttributes = {
                        type: type,
                        label: extBioField.name,
                        key: extBioField.parameter,
                        value: this.judicialFile?.data?.find((detail: DetailsDataEntity) => detail.parameter == extBioField.parameter)?.value || '',
                        detailId: this.judicialFile?.data?.find((detail: DetailsDataEntity) => detail.parameter == extBioField.parameter)?.id || '',
                        required: required,
                        options: options,
                        disabled: !this.canReadAndWrite || this.readOnly,
                        group: extBioField.group?.name ?? '',
                        groupRole: extBioField.group?.roleId ?? '',
                        tooltip: extBioField.description ?? '',
                        translations: this.managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'judicialFileFields')?.translations?.find((t: TranslationModel) => t.key === extBioField.parameter) || new TranslationModel(),
                    };
                    this.formAttributes.push(formAttribute);
                }
            });
            this.loggerService.debug(this.formAttributes);
            // If the action requires to show a form, render the dynamic form
            if (this.formAttributes.length > 0) {
                this.loggerService.info('There is a Form to Render');
                const formFields = this.convertFormControlArrayToObject(this.formAttributes);
                this.loggerService.debug(formFields);
                this.componentRef = this.dynamicFormContent.createComponent(DynamicFormComponent);
                this.componentRef.instance.controlsConfig = formFields;
                this.componentRef.instance.showForm = this.showDynamicForm;
                this.componentRef.instance.canReadAndWrite = this.canReadAndWrite && this.userIsVerified;
                this.componentRef.instance.userSubjectRoles = this.userSubject?.roles;
                this.componentRef.instance.groupTranslations = this.managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'judicialFileFieldGroups')?.translations || [];

                // Subscribe to the form submission event
                this.componentRef.instance.formSubmitted.subscribe((formData: any) => {
                    this.loggerService.debug('Form Data:');
                    this.loggerService.debug(formData);
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(formData) }
                    ];
                    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.SUBJECT, AuditTrailActions.MOD_JDF, ReasonActionTypeEnum.UPDATE, () => { this.updateJudicialFileDetails(formData); }, at_attributes);
                });
                // Subscribe to the form modified event
                this.componentRef.instance.formModified.subscribe((modified: boolean) => {
                    this.dynamicFormModified(modified);
                });
            }
        }
    }

    /* User Subject Details */
    updateJudicialFileDetails(formData: any) {
        this.loggerService.debug("Updating Judicial Files Details");
        Object.keys(formData).forEach((key: string) => {
            const dynamicFormField = this.formAttributes.find((attr: DynamicFormAttributes) => attr.key == key);
            const foundDetail = this.judicialFile?.data?.find((detail: DetailsDataEntity) => detail.id == dynamicFormField?.detailId);

            let detail: DetailsDataEntity = new DetailsDataEntity();
            //id
            detail.id = foundDetail ? foundDetail.id : (this.judicialFile?.data?.length! + 1).toString();
            //name
            detail.name = dynamicFormField?.label;
            //parameter
            detail.parameter = key;
            //value
            detail.value = formData[key].toString();
            if (foundDetail) {
                this.judicialFile?.data?.splice(this.judicialFile?.data?.indexOf(foundDetail), 1, detail);
            } else {
                this.judicialFile?.data?.push(detail);
            }
        });
        this.dynamicFormModified(false);
        this.trackDataChange();
        // this.messageService.add({
        //     severity: 'success',
        //     summary: this.translateService.instant('content.successTitle'),
        //     detail: this.translateService.instant('messages.success_general'),
        //     life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        // });
    }

    toggleDynamicForm() {
        if (this.componentRef) {
            this.componentRef.instance.canReadAndWrite = this.canReadAndWrite && this.userIsVerified;
            this.componentRef.instance.toggleFormState(this.canReadAndWrite && this.userIsVerified);
        }
    }

    isRequiredField(field: string, form: FormGroup): boolean {
        return this.validatorService.isRequiredField(form, field);
    }

    isValid(field: string, form: FormGroup): boolean {
        return this.validatorService.isValidField(form, field);
    }

    isValidDate(dateString: string): boolean {
      const date = new Date(dateString);
      return !isNaN(date.getTime()) && date.toISOString().split('T')[0] != (new Date(0)).toISOString().split('T')[0];
    }

    /**
     * Convert an array of form controls to an object
     * @param arr Array of form controls
     * @returns Object with the form controls
     */
    convertFormControlArrayToObject(arr: any[]) {
      const result: any = {};
      arr.forEach(item => {
        result[item.key] = item;
      });
      return result;
    }

    updateModified(modified: boolean) {
      this.modified = modified;
      this.contentModified.emit(this.modified)
    }

    dynamicFormModified(modified: boolean) {
        this.formModified = modified;
        if (modified) {
            this.form.disable();
        }
        else {
            this.form.enable();
        }
    }

    closeConfirmationDialog() {
        this.confirmationService.close();
        this.resetInactivityMonitor();
    }
}