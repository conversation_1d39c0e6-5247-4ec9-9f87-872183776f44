import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { ListFlowsComponent } from './list-flows/list-flows.component';
import { TagModule } from 'primeng/tag';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { TranslateModule } from '@ngx-translate/core';
import { MessagesModule } from 'primeng/messages';
import { ToastModule } from 'primeng/toast';
import { DropdownModule } from 'primeng/dropdown';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CalendarModule } from 'primeng/calendar';
import { TooltipModule } from 'primeng/tooltip';
import { DialogModule } from 'primeng/dialog';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InactivityMonitorModule } from 'src/verazial-common-frontend/modules/shared/components/inactivity-monitor/inactivity-monitor.module';


@NgModule({
  declarations: [
    ListFlowsComponent
  ],
  imports: [
    /* Angular */
    CommonModule,
    /* Translate */
    TranslateModule,
    /* Forms */
    FormsModule,
    ReactiveFormsModule,
    /* PrimeNG */
    TableModule,
    ButtonModule,
    TagModule,
    ConfirmDialogModule,
    MessagesModule,
    ToastModule,
    DropdownModule,
    IconFieldModule,
    InputIconModule,
    InputTextModule,
    CalendarModule,
    TooltipModule,
    DialogModule,
    InputSwitchModule,
    InputTextareaModule,
    /* Custom */
    InactivityMonitorModule,
  ],
  exports: [
    ListFlowsComponent
  ]
})
export class ListFlowsModule { }
