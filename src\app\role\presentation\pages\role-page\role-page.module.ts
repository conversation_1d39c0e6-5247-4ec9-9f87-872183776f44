import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { RolePageRoutingModule } from './role-page-routing.module';
import { RolePageComponent } from './role-page/role-page.component';
import { EmptyModule } from 'src/verazial-common-frontend/modules/shared/components/empty/empty.module';
import { LoadingSpinnerModule } from 'src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module';
import { ListRolesModule } from '../../components/list-roles/list-roles.module';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { StepsModule } from 'primeng/steps';
import { ToastModule } from 'primeng/toast';
import { GroupInfoModule } from 'src/app/group/presentation/components/group-info/group-info.module';
import { RoleModule } from '../../components/role/role.module';
import { MessagesModule } from 'primeng/messages';
import { InactivityMonitorModule } from 'src/verazial-common-frontend/modules/shared/components/inactivity-monitor/inactivity-monitor.module';


@NgModule({
  declarations: [
    RolePageComponent
  ],
  imports: [
    /* Angular */
    CommonModule,
    /* Routing */
    RolePageRoutingModule,
    /* Translate */
    TranslateModule,
    /* PrimeNG */
    ToastModule,
    ButtonModule,
    ConfirmDialogModule,
    DialogModule,
    StepsModule,
    MessagesModule,
    /* Custom */
    EmptyModule,
    LoadingSpinnerModule,
    ListRolesModule,
    GroupInfoModule,
    RoleModule,
    InactivityMonitorModule,
  ]
})
export class RolePageModule { }
