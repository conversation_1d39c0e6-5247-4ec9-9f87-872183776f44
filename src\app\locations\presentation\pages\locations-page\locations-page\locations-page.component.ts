import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ConfirmationService, MessageService, TreeNode } from 'primeng/api';
import { environment } from 'src/environments/environment';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailService } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { GetSettingsByApplicationUseCase } from 'src/verazial-common-frontend/core/general/manager/domain/use-cases/get-settings-by-application.use-case';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { GeneralSettings } from 'src/verazial-common-frontend/core/general/manager/common/models/general-settings.model';
import { ManagerSettingsEntity } from 'src/verazial-common-frontend/core/general/manager/domain/entity/manager-settings.model';
import { UpdateSettingsByIdUseCase } from 'src/verazial-common-frontend/core/general/manager/domain/use-cases/update-settings-by-id.use-case';
import { NewLocationModel } from 'src/verazial-common-frontend/core/general/manager/common/models/new-location.model';
import { v4 as uuidv4 } from 'uuid';
import { NewLocationsService } from 'src/verazial-common-frontend/core/services/new-locations.service';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';
import { GetKonektorPropertiesUseCase } from 'src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-locations-page',
  templateUrl: './locations-page.component.html',
  styleUrls: ['./locations-page.component.css'],
  providers: [ConfirmationService, MessageService]
})
export class LocationsPageComponent implements OnInit, OnDestroy {
  isLoading: boolean = false;
  locationsTree: TreeNode<any>[] = [];
  settings: ManagerSettingsEntity | undefined;
  config: GeneralSettings | undefined;
  cols: any[];
  frozenCols: any[];
  filterMode = 'lenient';

  files!: TreeNode[];
  cols3!: any[];

  editedManually: boolean = false;

  canSeeAllLocations: boolean = false;
  all_locations_idenfitier = AccessIdentifier.GET_ALL_LOCATIONS;

  confirmDialogTimeoutLimit: number = 0;
  startCheckingInactivity: boolean = false;

  constructor(
    private translate: TranslateService,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private auditTrailService: AuditTrailService,
    private getSettingsByApplicationUseCase: GetSettingsByApplicationUseCase,
    private updateSettingsByIdUseCase: UpdateSettingsByIdUseCase,
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService,
    private newLocationsService: NewLocationsService,
    private checkPermissions: CheckPermissionsService,
    private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,

  ) {
    this.cols = [
      { field: 'key', header: 'prisons_tab.location' },
      //{ field: 'occupiedBy', header: 'occupiedBy' },
      { field: '', header: '' },
    ];
    this.frozenCols = [
      { field: '', header: '' },
    ];
    this.isLoading = true;
    this.getLocations();
  }

  ngOnInit(): void {
  }

  ngOnDestroy() {
    // Clean up the timeout if the component is destroyed
    this.closeConfirmationDialog();
  }

  resetInactivityMonitor() {
    this.startCheckingInactivity = false;
    this.confirmDialogTimeoutLimit = 0;
  }

  async getLocations() {
    this.isLoading = true;

    try {
      // Retrieve both settings concurrently
      const [managerSettings, generalSettings] = await Promise.all([
        this.getSettingsByApplicationUseCase.execute({ applicationName: environment.application }),
        this.getSettingsByApplicationUseCase.execute({ applicationName: environment.generalSettings }),
      ]);

      // Process manager settings
      this.settings = managerSettings;

      // Process general settings
      this.config = generalSettings.settings;

      this.canSeeAllLocations = this.checkPermissions.hasReadPermissions(this.all_locations_idenfitier);

      var locs = this.settings?.settings?.continued1?.newLocations || [];

      this.loggerService.debug(locs);

      if (this.config?.locationsId) {

        if (this.canSeeAllLocations) {

          if(locs.length > 0 && locs[0].relatedTo) {
            const relatedToMap = new Map<string, NewLocationModel>();

            locs.forEach((location: NewLocationModel) => {
              if (location.relatedTo) {
                if (!relatedToMap.has(location.relatedTo)) {
                  const newParent = this.createNewLocation(location.relatedTo, '', uuidv4());
                  newParent.children = [];
                  relatedToMap.set(location.relatedTo, newParent);
                }
                relatedToMap.get(location.relatedTo)!.children!.push({ ...location, relatedTo: undefined });
              }
            });

            locs = [
              ...Array.from(relatedToMap.values()),
              ...locs.filter((location) => !location.relatedTo),
            ];

            this.loggerService.debug(locs);
            this.loggerService.debug(this.config.locationsId);
            locs = locs.filter((location) => this.config!.locationsId!.includes(location.name!));

            await this.saveAllLocations(locs);
          }
          else{
            var changed = false;
            this.config.locationsId.forEach((locationId: string) => {
              if (!locs.find((location) => location.name === locationId)) {
                changed = true;
                locs.push(this.createNewLocation(locationId, '', uuidv4()));
              }
            });

            locs = locs.filter((location) => this.config!.locationsId!.includes(location.name!));

            if(changed || locs.length != this.settings?.settings?.continued1?.newLocations?.length)
              await this.saveAllLocations(locs);
          }

        } else {

          try {
            const data = await firstValueFrom(this.getKonektorPropertiesUseCase.execute());
            const konektorProperties = data;

            const targetLocationId = konektorProperties.locationId!;
            this.loggerService.debug(targetLocationId);

            if (locs.length > 0) {

              if(locs[0].relatedTo) {
                const relatedToMap = new Map<string, NewLocationModel>();

                locs.forEach((location: NewLocationModel) => {
                  if (location.relatedTo === targetLocationId) {
                    this.loggerService.debug('relatedTo');
                    this.loggerService.debug(location);
                    if (!relatedToMap.has(targetLocationId)) {
                      const newParent = this.createNewLocation(targetLocationId, '', uuidv4());
                      newParent.children = [];
                      relatedToMap.set(targetLocationId, newParent);
                    }
                    relatedToMap.get(targetLocationId)!.children!.push({ ...location, relatedTo: undefined });
                  }
                });

                this.loggerService.debug('relatedToMap');
                this.loggerService.debug({...relatedToMap});

                if (relatedToMap.has(targetLocationId)) {
                  locs = [
                    relatedToMap.get(targetLocationId)!,
                  ];
                }
                this.loggerService.debug(locs);
              }
              else {
                this.loggerService.debug('else');
                locs = locs.filter((location) => location.name === targetLocationId);
              }
            }
          } catch (e) {
            this.loggerService.error('Error Getting Konektor Properties:');
            this.loggerService.error(e);
          }
        }

        this.locationsTree = this.newLocationsService.processLocations(locs);

        this.loggerService.debug(this.locationsTree);

      }
    } catch (e) {
      this.handleSettingsError(e);
    } finally {

      setTimeout(() => {
        this.isLoading = false;
      }, 200);
    }
  }



  private handleSettingsError(error: any) {
    this.loggerService.error('Error Retrieving Manager Settings: ' + error);
    this.auditTrailService.registerAuditTrailAction(
      this.localStorageService.getUser().numId,
      AuditTrailActions.GET_SETTINGS_BY_APPLICATION,
      0,
      'ERROR',
      '',
      [{ name: AuditTrailFields.ERROR, value: JSON.stringify(error) }]
    );
  }

  addSubcategories(node: any) {

    this.loggerService.debug(node);
    var nodeInTree = undefined;
    if (node.node.data.id)
      nodeInTree = this.newLocationsService.getNodeWithId(node.node.data.id, this.locationsTree);
    else
      nodeInTree = this.newLocationsService.getNodeWithkey(node.node.data.key, this.locationsTree);



    if (nodeInTree) {

      if (!nodeInTree.children) {
        node.children = [];
      }

      nodeInTree.expanded = true;
      nodeInTree.children!.push(
        {
          data: {
            key: `${node.node.data.key} ${nodeInTree.children!.length + 1}`,
            occupiedBy: [],
            id: uuidv4(),
          },
          expanded: false,
          children: [],
        }
      );

      this.locationsTree = [...this.locationsTree];

      this.saveNewLocations(nodeInTree);
    }
  }

  async saveAllLocations(locs: NewLocationModel[]): Promise<void> {
    if (this.settings?.settings?.continued1) {
      this.settings.settings.continued1.newLocations = locs;
      try {
        const data = await this.updateSettingsByIdUseCase.execute({
          id: this.settings.id!,
          settings: this.settings.settings,
        });
        this.localStorageService.setSessionSettings(data.settings!);
        this.messageService.add({
          severity: 'success',
          summary: this.translate.instant('messages.location_created'),
        });
      } catch (e) {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('messages.location_created_error'),
        });
      }
    }
  }

  saveNewLocations(node: TreeNode) {
    if (this.settings?.settings?.continued1) {
      let locs = this.settings?.settings?.continued1?.newLocations || [];


        const loc = this.findLocationInTree(locs, node.data.id!);
        if (loc) {
          const lastChild = node.children?.[node.children.length - 1];
          if (lastChild && loc) {
            loc.children = loc.children || [];
            loc.children.push(this.createNewLocation(lastChild.data.key!, "", lastChild!.data.id!));
          }
        }
        else {

          const location = this.findLocationInTree(locs, node.data.id!);

          if (location) {
            const lastChild = node.children?.[node.children.length - 1];
            if (lastChild && location) {
              location.children = location.children || [];
              location.children.push(this.createNewLocation(lastChild.data.key!, "", lastChild!.data.id!));
            }
          }
          else {
            this.loggerService.error('Error saving new location: Location not found');
          }

        }



      this.settings.settings.continued1.newLocations = locs;
      //this.settings.settings.continued1.newLocations = [];
      this.updateSettingsByIdUseCase.execute({
        id: this.settings?.id!,
        settings: this.settings?.settings!,
      }).then(
        (data) => {
          this.localStorageService.setSessionSettings(data.settings!);
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('messages.location_created'),
          });
        },
        (e) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('messages.location_created_error'),
          });
        },
      );
    }
  }

  findLocationInTree(locations: NewLocationModel[], locationId: string): NewLocationModel | undefined {
    for (const loc of locations) {
      if (loc.id === locationId) {
        return loc;
      }

      if (loc.children) {
        const foundInChildren = this.findLocationInChildren(loc.children, locationId);
        if (foundInChildren) {
          return foundInChildren;
        }
      }
    }
    return undefined;
  }

  private findLocationInChildren(children: NewLocationModel[] | undefined, locationId: string): NewLocationModel | undefined {
    if (!children) return undefined;
    for (const child of children) {
      if (child.id === locationId) {
        return child;
      }
      // Recursively search in child's children
      const foundInChildChildren = this.findLocationInChildren(child.children, locationId);
      if (foundInChildChildren) {
        return foundInChildChildren;
      }
    }
    return undefined;
  }

  createNewLocation(name: string, relatedTo: string, id: string): NewLocationModel {

    var newLocation = new NewLocationModel();

    newLocation.name = name;
    newLocation.relatedTo = relatedTo;
    newLocation.id = id;
    newLocation.children = [];
    return newLocation;
  }

  deleteNode(node: TreeNode) {

    this.editedManually = true;

    if (this.checkIfLocationIsOccupied(node)) {
      this.confirmationService.confirm({
        message: `${this.translate.instant('messages.no_delete_location')}`,
        header: this.translate.instant('messages.delete_confirmation_header'),
        icon: 'pi pi-exclamation-triangle',
        rejectButtonStyleClass: "p-button-text",
        acceptButtonStyleClass: "ng-confirm-button",
        acceptIcon: "none",
        rejectIcon: "none",
        acceptLabel: this.translate.instant("accept"),
        rejectVisible: false,
        accept: () => {

        },
        reject: () => {
          this.resetInactivityMonitor();
        }
      });

      this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
      this.startCheckingInactivity = false;
      setTimeout(() => this.startCheckingInactivity = true, 0);
    }
    else {

      this.confirmationService.confirm({
        message: `${this.translate.instant('messages.delete_location')} <b>${node.key}</b>?`,
        header: this.translate.instant('messages.delete_confirmation_header'),
        icon: 'pi pi-exclamation-triangle',
        rejectButtonStyleClass: "p-button-text",
        acceptButtonStyleClass: "ng-confirm-button",
        acceptIcon: "none",
        rejectIcon: "none",
        acceptLabel: this.translate.instant("delete"),
        rejectLabel: this.translate.instant("no"),
        accept: () => {
          this.removeNodeFromTree(this.locationsTree, node);
          this.locationsTree = [...this.locationsTree];

          this.deleteLocationsFromBackend(node); // Call the new function to handle deletion in the backend
        },
        reject: () => {
          this.resetInactivityMonitor();
        }
      });

      this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
      this.startCheckingInactivity = false;
      setTimeout(() => this.startCheckingInactivity = true, 0);
    }
  }

  checkIfLocationIsOccupied(node: any): boolean {
    var tree = this.getTreeFromNode(node);
    this.loggerService.debug(tree!);
    return this.isNodeOrChildrenOccupied(tree!);
  }

  isNodeOrChildrenOccupied(node: TreeNode): boolean {
    if (node.data.occupiedBy && node.data.occupiedBy.length > 0) {
      return true;
    }

    if (node.children && node.children.length > 0) {
      return node.children.some(child => this.isNodeOrChildrenOccupied(child));
    }

    return false;
  }

  getTreeFromNode(node: any): TreeNode | null {
    const findNodeById = (tree: TreeNode[], targetId: string): TreeNode | null => {
      for (const item of tree) {
        if (item.data.id === targetId) {
          return item;
        }
        if (item.children && item.children.length > 0) {
          const found = findNodeById(item.children, targetId);
          if (found) {
            return found;
          }
        }
      }
      return null;
    };

    return findNodeById(this.locationsTree, node.id);
  }

  deleteLocationsFromBackend(node: any) {
    if (this.settings?.settings?.continued1) {
      let locs = this.settings.settings.continued1.newLocations || [];

      const locsLength = locs.length;
      // Remove from top-level locations
      locs = locs.filter(location => location.id !== node.id);

      if (locs.length === locsLength) {
        // Check and remove from children
        locs.forEach(location => {
          if (location.children) {
            location.children = this.removeLocationFromTree(location.children, node.id);
          }
        });
      }

      this.settings.settings.continued1.newLocations = locs;

      // Update the backend
      this.updateSettingsByIdUseCase.execute({
        id: this.settings.id!,
        settings: this.settings.settings!,
      }).then(
        (data) => {
          this.localStorageService.setSessionSettings(data.settings!);
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('messages.location_deleted'),
          });
        },
        (error) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('messages.location_deleted_error'),
          });
          this.loggerService.error('Error Saving Manager Settings: ' + error);
        },
      );
    }
  }

  // Helper to remove segments recursively
  private removeLocationFromTree(locations: NewLocationModel[], id: string): NewLocationModel[] {
    return locations.filter(location => {
      if (location.id === id) {
        return false; // Remove the segment
      }
      if (location.children) {
        location.children = this.removeLocationFromTree(location.children, id);
      }
      return true;
    });
  }


  private removeNodeFromTree(tree: TreeNode[], nodeDelete: any, first = true): void {
    var index = -1;
    if (first) {
      index = tree.findIndex(node => node.data.key === nodeDelete.key);
    }
    else {
      index = tree.findIndex(node => node.data.id === nodeDelete.id);
    }
    if (index !== -1) {
      tree.splice(index, 1);
    } else {
      tree.forEach(node => {
        if (node.children) {
          this.removeNodeFromTree(node.children, nodeDelete, false);
        }
      });
    }
  }


  onEditComplete(event: any) {

    if (this.editedManually) {
      this.editedManually = false;
      return;
    }

    var nodeInTree = undefined;
    if (event.data.id)
      nodeInTree = this.newLocationsService.getNodeWithId(event.data.id, this.locationsTree);

    if (!nodeInTree)
      return;


    if (nodeInTree.data.key !== event.data.key) {
      nodeInTree.data.key = event.data.key;
    }


    this.locationsTree = [...this.locationsTree];

    this.updateLocationsInBackend(event.data).then((success) => {

      if (success)
        this.messageService.add({
          severity: 'success',
          summary: this.translate.instant('messages.location_updated'),
        });
      else
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('messages.location_updated_error'),
        });
    });
  }

  async updateLocationsInBackend(event: any): Promise<boolean> {
    if (this.settings?.settings?.continued1) {
      let locs = this.settings.settings.continued1.newLocations || [];

      this.newLocationsService.updateLocationNameById(locs, event.id, event.key);

      this.settings.settings.continued1.newLocations = locs;

      try {
        const data = await this.updateSettingsByIdUseCase.execute({
          id: this.settings.id!,
          settings: this.settings.settings,
        }).then(
          (data) => {
            this.localStorageService.setSessionSettings(data.settings!);
          },
          (error) => {
            this.loggerService.error('Error Saving Manager Settings: ' + error);
            return false;
          }
        );
        return true;
      } catch (e) {
        this.loggerService.error('Error Saving Manager Settings: ' + e);
        // Show error
        return false;
      }
    } else {
      return false;
    }
  }

  closeConfirmationDialog() {
    this.confirmationService.close();
    this.resetInactivityMonitor();
  }
}
