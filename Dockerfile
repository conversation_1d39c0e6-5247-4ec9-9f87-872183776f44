FROM node:20-alpine AS build
WORKDIR /app
COPY package.json package-lock.json ./
COPY ngx-verazial-ui-lib-1.0.1.tgz ./
COPY drawflow-0.0.50.tgz ./
RUN npm install ngx-verazial-ui-lib-1.0.1.tgz
RUN npm install drawflow-0.0.50.tgz
RUN npm install

COPY . .

RUN npm install -g @angular/cli

RUN npm run build --prod

# Serve Application using Nginx Server
FROM nginx:alpine
COPY --from=build /app/dist/verazial-app/browser/ /usr/share/nginx/html

# Copy the default Nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY security-headers.conf /etc/nginx/security-headers.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
