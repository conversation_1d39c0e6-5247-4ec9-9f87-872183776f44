import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { InputTextModule } from 'primeng/inputtext';
import { SidebarModule } from 'primeng/sidebar';
import { BadgeModule } from 'primeng/badge';
import { RadioButtonModule } from 'primeng/radiobutton';
import { InputSwitchModule } from 'primeng/inputswitch';
import { RippleModule } from 'primeng/ripple';
import { AppMenuComponent } from './components/menu/app.menu.component';
import { AppMenuitemComponent } from './components/menu/app.menuitem.component';
import { RouterModule } from '@angular/router';
import { AppTopBarComponent } from './components/topbar/app.topbar.component';
import { AppConfigModule } from './config/config.module';
import { AppLayoutComponent } from "./app.layout.component";
import { TieredMenuModule } from 'primeng/tieredmenu';
import { DialogModule } from 'primeng/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { AvatarModule } from 'primeng/avatar';
import { AppFooterComponent } from './components/footer/app.footer.component';
import { AppSidebarComponent } from './components/sidebar/app.sidebar.component';
import { TooltipModule } from 'primeng/tooltip';
import { LanguageTranslatorModule } from 'src/verazial-common-frontend/modules/shared/components/language-translator/language-translator.module';
import { RoleSelectionModule } from 'src/verazial-common-frontend/modules/shared/components/role-selection/role-selection.module';
import { DropdownModule } from 'primeng/dropdown';


@NgModule({
    declarations: [
        AppMenuitemComponent,
        AppTopBarComponent,
        AppFooterComponent,
        AppMenuComponent,
        AppSidebarComponent,
        AppLayoutComponent,
    ],
    imports: [
        BrowserModule,
        FormsModule,
        HttpClientModule,
        BrowserAnimationsModule,
        InputTextModule,
        SidebarModule,
        BadgeModule,
        RadioButtonModule,
        InputSwitchModule,
        RippleModule,
        RouterModule,
        AppConfigModule,
        TieredMenuModule,
        LanguageTranslatorModule,
        TranslateModule,
        DialogModule,
        RoleSelectionModule,
        AvatarModule,
        TooltipModule,
        DropdownModule,
    ],
    exports: [AppLayoutComponent]
})
export class AppLayoutModule { }
