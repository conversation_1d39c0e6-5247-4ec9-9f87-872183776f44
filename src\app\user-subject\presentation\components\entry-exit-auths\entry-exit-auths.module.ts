import { NgModule } from "@angular/core";
import { EntryExitAuthsListComponent } from "./entry-exit-auths-list/entry-exit-auths-list.component";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { ProgressSpinnerModule } from "primeng/progressspinner";
import { DialogModule } from "primeng/dialog";
import { ButtonModule } from "primeng/button";
import { TableModule } from "primeng/table";
import { IconFieldModule } from "primeng/iconfield";
import { InputIconModule } from "primeng/inputicon";
import { InputTextModule } from "primeng/inputtext";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { DropdownModule } from "primeng/dropdown";
import { InputSwitchModule } from "primeng/inputswitch";
import { CalendarModule } from "primeng/calendar";
import { TreeSelectModule } from "primeng/treeselect";
import { TooltipModule } from "primeng/tooltip";
import { ToastModule } from "primeng/toast";
import { EmptyModule } from "src/verazial-common-frontend/modules/shared/components/empty/empty.module";
import { EntryExitAuthEditComponent } from "./entry-exit-auth-edit/entry-exit-auth-edit.component";
import { LoadingSpinnerModule } from "src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module";
import { BioSignaturesModule } from "src/verazial-common-frontend/modules/shared/components/bio-signatures/bio-signatures.module";
import { SelectButtonModule } from "primeng/selectbutton";
import { StepsModule } from "primeng/steps";
import { ScrollPanelModule } from "primeng/scrollpanel";
import { InputTextareaModule } from "primeng/inputtextarea";
import { InactivityMonitorModule } from "src/verazial-common-frontend/modules/shared/components/inactivity-monitor/inactivity-monitor.module";

@NgModule({
    declarations: [
        EntryExitAuthsListComponent,
        EntryExitAuthEditComponent,
    ],
    imports: [
      /* Angular Modules */
      CommonModule,
      /* Forms */
      ReactiveFormsModule,
      FormsModule,
      /* Translate */
      TranslateModule,
      /* PrimeNG Modules */
      ProgressSpinnerModule,
      DialogModule,
      ButtonModule,
      TableModule,
      IconFieldModule,
      InputIconModule,
      InputTextModule,
      ConfirmDialogModule,
      DropdownModule,
      InputSwitchModule,
      CalendarModule,
      TreeSelectModule,
      TooltipModule,
      ToastModule,
      InputTextareaModule,
      ScrollPanelModule,
      StepsModule,
      SelectButtonModule,
      /* Custom Modules */
      EmptyModule,
      LoadingSpinnerModule,
      BioSignaturesModule,
      InactivityMonitorModule,
    ],
    exports: [
        EntryExitAuthsListComponent,
        EntryExitAuthEditComponent,
    ]
  })
  export class EntryExitAuthsModule { }