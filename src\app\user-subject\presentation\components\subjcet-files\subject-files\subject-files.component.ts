import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, Validators } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { ConfirmationService, MessageService, PrimeNGConfig } from "primeng/api";
import { FileSelectEvent, FileUploadHandlerEvent } from "primeng/fileupload";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { LanguageRecordModel, TranslationGroup, TranslationModel } from "src/verazial-common-frontend/core/general/manager/common/models/translation.model";
import { CreateRawStaticResourceEntity } from "src/verazial-common-frontend/core/general/storage/domain/entity/create-raw-static-resource.entity";
import { CreateStaticResourceEntity } from "src/verazial-common-frontend/core/general/storage/domain/entity/create-static-resource.entity";
import { StaticResourceEntity } from "src/verazial-common-frontend/core/general/storage/domain/entity/static-resource.entity";
import { CreateStaticResourceUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/create-static-resource.use-case";
import { DeleteStaticResourcesBySubjectIdAndNameAndNumberUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/delete-static-resource-by-subject-id-and-name-and-number.use-case";
import { GetStaticResourcesBySubjectIdAndNameUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/get-static-resources-by-subject-id-and-name.use-case";
import { GetRawStaticResourceByIdUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/get-raw-static-resource-by-id.use-case";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { GetStaticResourcesBySubjectIdAndNameAndNumberUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/get-static-resources-by-subject-id-and-name-and-number-use-case";
import { getFileExtensionFromMimeType } from "src/verazial-common-frontend/core/util/supporting-functions";

@Component({
    selector: 'app-subject-files',
    templateUrl: './subject-files.component.html',
    styleUrl: './subject-files.component.css',
    providers: [MessageService, ConfirmationService]
})
export class SubjectFilesComponent implements OnInit, OnDestroy {
    @Input() readAndWritePermissions: boolean = false;
    @Input() userSubject: SubjectEntity | undefined;
    @Input() userIsVerified: boolean = false;
    @Input() subjectIsVerified: boolean = false;
    @Input() managerSettings?: GeneralSettings;

    /** Flags */
    isLoading: boolean = false;
    showUploadDialog: boolean = false;
    isLoadingNewSubjectFile: boolean = false;
    showPreviewDialog: boolean = false;

    /** Data */
    subjectFileTypes: string[] = [];
    subjectFiles: {
        type: string;
        files: StaticResourceEntity[];
    }[] = [];
    newSubjectFile: {
        type: string;
        files: StaticResourceEntity;
    } = {
        type: '',
        files: new StaticResourceEntity()
    };
    previewFile?: StaticResourceEntity;
    previewUrl?: string;
    acceptedFiles: string = 'image/*,audio/*,video/*,text/*,application/pdf';
    maxFileSize: string = '10400000'; // 10MB

    /** Upload New */
    // public form: FormGroup = this.fb.group({
    //     alias: ['', [Validators.required]],
    //     description: ['', [Validators.required]],
    // });

    // Date Range Filter
    formGroupDate: FormGroup = new FormGroup({
        date: new FormControl<Date[] | null>(null)
    });
    formGroupDate2: FormGroup = new FormGroup({
        date: new FormControl<Date[] | null>(null)
    });
    dateFilterValues = {
        startDate: null,
        endDate: null
    };
    rangeDates: Date[] | null = null;

    confirmDialogTimeoutLimit: number = 0;
    startCheckingInactivity: boolean = false;

    constructor(
        private translateService: TranslateService,
        private loggerService: ConsoleLoggerService,
        private fb: FormBuilder,
        private messageService: MessageService,
        private confirmationService: ConfirmationService,
        private localStorageService: LocalStorageService,
        private auditTrailService: AuditTrailService,
        private createStaticResourceUseCase: CreateStaticResourceUseCase,
        private getStaticResourcesBySubjectIdAndNameUseCase: GetStaticResourcesBySubjectIdAndNameUseCase,
        private getStaticResourcesBySubjectIdAndNameAndNumberUseCase: GetStaticResourcesBySubjectIdAndNameAndNumberUseCase,
        private deleteStaticResourceBySubjectIdAndNameAndNumberUseCase: DeleteStaticResourcesBySubjectIdAndNameAndNumberUseCase,
    ) { }

    ngOnInit(): void {
        this.subjectFileTypes = this.managerSettings?.subjectTabsConfig?.subjectFileTypes ?? [];
        this.acceptedFiles = this.managerSettings?.subjectTabsConfig?.acceptedFiles ?? 'image/*,audio/*,video/*,text/*,application/pdf';
        this.maxFileSize = this.managerSettings?.subjectTabsConfig?.maxFileSize ?? '10400000'; // 10MB
        this.loadFiles();
    }

    ngOnDestroy() {
        // Clean up the timeout if the component is destroyed
        this.closeConfirmationDialog();
    }

    resetInactivityMonitor() {
        this.startCheckingInactivity = false;
        this.confirmDialogTimeoutLimit = 0;
    }

    loadFiles() {
        this.subjectFiles = [];
        for (let type of this.subjectFileTypes) {
            this.isLoading = true;
            let subjectFileGroup: {
                type: string;
                files: StaticResourceEntity[];
            } = {
                type: type,
                files: []
            }
            this.getStaticResourcesBySubjectIdAndNameUseCase.execute({ subjectId: this.userSubject?.id!, name: type }).then(
                (data) => {
                    console.log(data);
                    if (data) {
                        subjectFileGroup.files = data.sort((a, b) => {
                            if (a.number === undefined) return -1;  // Push 'undefined' to the end
                            if (b.number === undefined) return 1; // Push 'undefined' to the end
                            return b.number - a.number;            // Regular comparison if both are defined
                        });
                    }
                    this.subjectFiles.push(subjectFileGroup);
                },
                (e) => {
                    this.loggerService.error('Error Getting User/Subject Static Resources:');
                    this.loggerService.error(e);

                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.SUBJECT_ID, value: this.userSubject?.id! },
                        { name: AuditTrailFields.RECORD_NAME, value: type }
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_STATIC_RESOURCE_BY_SUBJECT_ID_AND_NAME, 0, 'ERROR', '', at_attributes);
                }
            )
            .finally(() => {
                // Sort subject files alphabetically by type
                this.subjectFiles.sort((a, b) => {
                    return this.getLabel(a.type).localeCompare(this.getLabel(b.type));
                });
                this.isLoading = false;
            });
        }
    }

    uploadNew(event: string) {
        this.newSubjectFile = {
            type: event,
            files: new StaticResourceEntity()
        };
        this.showUploadDialog = true;
    }

    onUpload(result: {event: FileUploadHandlerEvent, file: StaticResourceEntity}) {
        this.isLoadingNewSubjectFile = true;
        this.loggerService.debug("Uploaded file");
        this.loggerService.debug(result);
        const file = result.event.files[0];
        const reader = new FileReader();

        reader.onload = () => {
            // The result is a Base64 string with a prefix
            this.newSubjectFile.files.content = (reader.result as string).replace('data:' + file.type + ';base64,', '');

            let number = 0;
            this.subjectFiles.find(f => f.type == this.newSubjectFile.type)?.files.forEach(f => {
                if (f.number! > number) {
                    number = f.number!;
                }
            });
            number++;

            let createStaticResourceRequest: CreateStaticResourceEntity = {
                subjectId: this.userSubject?.id!,
                name: this.newSubjectFile.type,
                number: number,
                alias: result.file.alias || file.name,
                description: result.file.description || '',
                mimeType: file.type,
                content: this.newSubjectFile.files.content,
                async: false,
            }
            this.loggerService.debug("Creating raw static resource");
            this.loggerService.debug(createStaticResourceRequest);
            this.createStaticResourceUseCase.execute({ createStaticResourceRequest: createStaticResourceRequest }).then(
                () => {
                    this.messageService.add({
                        severity: 'success',
                        summary: this.translateService.instant('titles.success_general'),
                        detail: this.translateService.instant('messages.fileUploadedSuccess').replace('{0}', file.name),
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                    this.loggerService.debug("Created static resource");
                    this.loadFiles();
                    this.onCancelDialog();
                    createStaticResourceRequest.content = undefined;
                    let at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(createStaticResourceRequest) },
                        { name: AuditTrailFields.RECORD_NAME, value: createStaticResourceRequest.name! },
                        { name: AuditTrailFields.RECORD_NUMBER, value: createStaticResourceRequest.number!.toString() }
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_STA_RES, 0, 'SUCCESS', '', at_attributes);
                },
                (e) => {
                    this.loggerService.debug("Error creating raw static resource");
                    this.loggerService.debug(e);
                    this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('content.errorTitle'),
                        detail: this.translateService.instant('messages.fileUploadedError').replace('{0}', file.name),
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                    createStaticResourceRequest.content = undefined;
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(createStaticResourceRequest) },
                        { name: AuditTrailFields.RECORD_NAME, value: createStaticResourceRequest.name! },
                        { name: AuditTrailFields.RECORD_NUMBER, value: createStaticResourceRequest.number!.toString() }
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_STA_RES, 0, 'ERROR', '', at_attributes);
                }
            )
            .finally(() => {
                this.isLoadingNewSubjectFile = false;
            });
        };
        reader.onerror = (error) => {
            this.isLoadingNewSubjectFile = false;
            this.loggerService.error('Error reading file:');
            this.loggerService.error(error);
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('content.errorTitle'),
                detail: this.translateService.instant('messages.fileUploadedError').replace('{0}', file.name),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
        };

        // Read the file as a Data URL
        reader.readAsDataURL(file);
    }

    onEdit(data: StaticResourceEntity) {
        this.loggerService.debug(data);
    }

    onDelete(data: StaticResourceEntity) {
        this.loggerService.debug(data);
        this.confirmationService.confirm({
            message: this.translateService.instant('messages.message_remove') + " <b>" + data.alias + `</b>?`,
            header: this.translateService.instant('messages.delete_single_record'),
            icon: 'pi pi-exclamation-triangle',
            acceptIcon: "none",
            rejectIcon: "none",
            rejectButtonStyleClass: "p-button-text",
            acceptButtonStyleClass: "ng-confirm-button",
            acceptLabel: this.translateService.instant('delete'),
            rejectLabel: this.translateService.instant('no'),
            accept: () => {
                let auditTrailData = data;
                auditTrailData.content = undefined;
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(auditTrailData) },
                ];
                this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.SUBJECT, AuditTrailActions.DEL_STA_RES, ReasonActionTypeEnum.DELETE, () => { this.onSubmitDelete(data); }, at_attributes);
            },
            reject: () => {
                this.resetInactivityMonitor();
            }
        });

        this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
        this.startCheckingInactivity = false;
        setTimeout(() => this.startCheckingInactivity = true, 0);
    }

    onSubmitDelete(data: StaticResourceEntity) {
        if (data) {
            this.isLoading = true;
            const param = {
                subjectId: data.subjectId!,
                name: data.name!,
                number: data.number!
            }
            this.deleteStaticResourceBySubjectIdAndNameAndNumberUseCase.execute(param).then(
                () => {
                    this.messageService.add({
                        severity: 'success',
                        summary: this.translateService.instant('content.successTitle'),
                        detail: this.translateService.instant('messages.success_general'),
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                    this.loadFiles();
                },
                (e) => {
                    this.loggerService.error('Error Deleting User/Subject Static Resource:');
                    this.loggerService.error(e);
                    const at_attributes: ExtraData[] = [
                        { name: 'error', value: JSON.stringify(e) },
                        { name: 'static_resource_name', value: param.name },
                        { name: 'static_resource_number', value: param.number.toString() }
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_STA_RES, 0, 'ERROR', '', at_attributes);
                    this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('content.errorTitle'),
                        detail: `${this.translateService.instant('messages.error_removing_image')}: ${e.message}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                }
            )
            .finally(() => {
                this.isLoading = false;
            });
        }
    }

    onCancelDialog() {
        this.showUploadDialog = false;
        this.newSubjectFile = {
            type: '',
            files: new StaticResourceEntity()
        };
    }

    onCancelPreviewDialog() {
        this.previewFile = undefined;
        this.previewUrl = undefined;
        this.showPreviewDialog = false;
    }

    onSelectedFiles(event: FileSelectEvent) {
        this.loggerService.debug(event);
        let file = event.currentFiles[0];
        this.newSubjectFile.files.mimeType = file.type;
    }

    getLabel(key: string): string {
        let reasonTranslations = this.managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'subjectFileTypes')?.translations || [];
        let translations: LanguageRecordModel[] = reasonTranslations.find((t: TranslationModel) => t.key === key)?.translations || [];
        let translation = translations.find(t => t.languageCode == this.translateService.currentLang);
        if (translation && translation.value) {
            return translation.value
        }
        return key;
    }

    onPreview(data: StaticResourceEntity) {
        this.loggerService.debug(data);
        this.previewFile = data;
        this.previewUrl = `data:${data.mimeType};base64,${data.content}`;
        this.showPreviewDialog = true;
    }

    onDownload(data: StaticResourceEntity) {
        this.loggerService.debug(data);
        // Get the raw static resource with full content
        if (data.content) {
            this.isLoading = true;
            try {
                let staticResource = data;
                // Convert base64 to blob
                const byteCharacters = atob(staticResource.content!);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                const blob = new Blob([byteArray], { type: staticResource.mimeType || 'application/octet-stream' });

                // Create download link
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;

                // Use alias as filename, fallback to a default name
                const filename = data.alias || `file_${data.number || 'unknown'}`;

                // Add appropriate file extension based on mime type if not present
                let downloadFilename = filename;
                if (staticResource.mimeType && !filename.includes('.')) {
                    const extension = getFileExtensionFromMimeType(staticResource.mimeType);
                    if (extension) {
                        downloadFilename = `${filename}.${extension}`;
                    }
                }

                link.download = downloadFilename;
                link.style.display = 'none';

                // Trigger download
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Clean up the URL object
                URL.revokeObjectURL(url);

                this.messageService.add({
                    severity: 'success',
                    summary: this.translateService.instant('content.successTitle'),
                    detail: this.translateService.instant('messages.fileDownloadedSuccess').replace('{0}', downloadFilename),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.isLoading = false;
            } catch (error: any) {
                this.isLoading = false;
                this.loggerService.error('Error processing file for download:');
                this.loggerService.error(error);
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('content.errorTitle'),
                    detail: this.translateService.instant('messages.error_downloading_file') + ': ' + error.message,
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
            }
        } else {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('content.errorTitle'),
                detail: this.translateService.instant('messages.fileContentNotFound'),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
        }
    }

    /* Search */
    onFilter(event: any) {
        if (!event.filters['updatedAt'].value) {
            this.rangeDates = null;
            this.formGroupDate.reset();
        }
        if (!event.filters['createdAt'].value) {
            this.rangeDates = null;
            this.formGroupDate2.reset();
        }
    }

    closeConfirmationDialog() {
        this.confirmationService.close();
        this.resetInactivityMonitor();
    }

    /**
     * viewer: google, office, mammoth, pdf or url
     * File type support
     * google viewer: .TXT, .CSS, .HTML, .PHP, .C, .CPP, .H, .HPP, .JS, .DOC, .DOCX,.XLS, .XLSX
     *                .PPT, .PPTX, .PDF, .PAGES, .AI, .PSD, .TIFF, .DXF, .SVG, .EPS, .PS, TTF, .XPS
     *                .ZIP, .RAR
     * office viewer: .ppt, .pptx, .doc, .docx, .xls, .xlsx
     * mammoth: .docx
     * pdf: .pdf
     * url: For another external document viewers that should be loaded in an iframe.
     */
    getViewer(mimeType: string): string {
        if (mimeType.startsWith('image/')) {
            return 'url';
        }
        if (mimeType.startsWith('audio/')) {
            return 'url';
        }
        if (mimeType.startsWith('video/')) {
            return 'url';
        }
        if (mimeType.startsWith('text/')) {
            return 'url';
        }
        if (mimeType.startsWith('application/pdf')) {
            return 'pdf';
        }
        let supportedExtentions: string[] = [
            ".TXT", ".CSS", ".HTML", ".PHP", ".C", ".CPP", ".H", ".HPP", ".JS", ".DOC", ".DOCX,.XLS", ".XLSX",
            ".PPT", ".PPTX", ".PDF", ".PAGES", ".AI", ".PSD", ".TIFF", ".DXF", ".SVG", ".EPS", ".PS, TTF", ".XPS",
            ".ZIP", ".RAR", ".ppt", ".pptx", ".doc", ".docx", ".xls", ".xlsx"
        ];
        if (supportedExtentions.includes(getFileExtensionFromMimeType(mimeType) || '')) {
            return 'office';
        }
        return 'url';
    }
}
