<p-confirmDialog />
<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<div *ngIf="showForm" class="px-6">
    <p-accordion [multiple]="true" activeIndex="{{ formInStepper ? 0 : null }}">
        <div *ngFor="let f of forms; let i = index" class="mb-1">
            <p-accordionTab class="alternative-accordion-style" header="{{ f.gorup != '' ? getGroupHeader(f.group) : ('headers.general' | translate) }}">
                <form [formGroup]="f.form" class="flex flex-column align-items-center mt-1">
                    <div class="w-full">
                        <div class="grid flex align-items-center justify-content-center">
                            <ng-container *ngFor="let key of objectKeys(getGroupControls(f.group))">
                                <ng-container [ngSwitch]="controlsConfig[key].type">
                                    <!-- Text Input -->
                                    <div *ngSwitchCase="inputField" class="flex flex-column gap-2 lg:col-6 col-12 width90">
                                        <div class="flex align-items-center gap-2">
                                            <label [for]="key">{{getLabel(key)}}</label>
                                            <i *ngIf="controlsConfig[key].tooltip" class="pi pi-info-circle" pTooltip="{{controlsConfig[key].tooltip}}" tooltipPosition="top" showDelay="300"></i>
                                        </div>
                                        <input
                                            [ngClass]="!isValid(key, i) ? 'ng-invalid ng-dirty':'' "
                                            type="text" pInputText [id]="key" [formControlName]="key"
                                        />
                                        <div *ngIf="!isValid(key, i)">
                                            <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                                        </div>
                                    </div>

                                    <!-- Text Area Input -->
                                    <div *ngSwitchCase="textareaField" class="flex flex-column gap-2 col-12 width90">
                                        <div class="flex align-items-center gap-2">
                                            <label [for]="key">{{getLabel(key)}}</label>
                                            <i *ngIf="controlsConfig[key].tooltip" class="pi pi-info-circle" pTooltip="{{controlsConfig[key].tooltip}}" tooltipPosition="top" showDelay="300"></i>
                                        </div>
                                        <textarea
                                            [ngClass]="!isValid(key, i) ? 'ng-invalid ng-dirty':'' "
                                            rows="5" cols="30" pInputTextarea [id]="key" [formControlName]="key" [autoResize]="true"
                                        ></textarea>
                                        <div *ngIf="!isValid(key, i)">
                                            <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                                        </div>
                                    </div>

                                    <!-- Dropdown -->
                                    <div *ngSwitchCase="dropdownField" class="flex flex-column gap-2 lg:col-6 col-12 width90">
                                        <div class="flex align-items-center gap-2">
                                            <label [for]="key">{{getLabel(key)}}</label>
                                            <i *ngIf="controlsConfig[key].tooltip" class="pi pi-info-circle" pTooltip="{{controlsConfig[key].tooltip}}" tooltipPosition="top" showDelay="300"></i>
                                        </div>
                                        <p-dropdown
                                            [ngClass]="!isValid(key, i) ? 'ng-invalid ng-dirty':'' "
                                            appendTo="body" [id]="key" [options]="controlsConfig[key].options" [formControlName]="key"
                                            [placeholder]="getLabel(key)" [checkmark]="true" [showClear]="true" [style]="{'width': '250px'}"
                                        />
                                        <div *ngIf="!isValid(key, i)">
                                            <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                                        </div>
                                    </div>

                                    <!-- Switch -->
                                    <div *ngSwitchCase="toggleField" class="flex flex-column align-items-end gap-2 col-6 col-offset-6 width90">
                                        <div class="flex align-items-center gap-2">
                                            <label [for]="key" class="pl-4">{{getLabel(key)}}</label>
                                            <i *ngIf="controlsConfig[key].tooltip" class="pi pi-info-circle" pTooltip="{{controlsConfig[key].tooltip}}" tooltipPosition="top" showDelay="300"></i>
                                        </div>
                                        <div class="flex justify-content-center pl-4">
                                            <p-inputSwitch
                                                [ngClass]="!isValid(key, i) ? 'ng-invalid ng-dirty':'' "
                                                [id]="key" [formControlName]="key"
                                            />
                                            <div *ngIf="!isValid(key, i)">
                                                <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                                            </div>
                                        </div>
                                    </div>
                                </ng-container>
                            </ng-container>
                            <!-- Buttons Button -->
                            <div *ngIf="canReadAndWrite && !formInStepper" class="flex justify-content-end col-12">
                                <button pButton pRipple icon="pi pi-save" label="{{ 'save' | translate }}" (click)="onSubmit(i, f.group)" class="p-button-text" [disabled]="f.invalid"></button>
                                <button pButton pRipple icon="pi pi-times" label="{{ 'cancel' | translate }}" (click)="onCancel(i, f.group)" class="p-button-text"></button>
                            </div>
                        </div>
                    </div>
                </form>
            </p-accordionTab>
        </div>
    </p-accordion>
    <div *ngIf="canReadAndWrite && formInStepper">
        <div class="flex pt-4 align-items-center justify-content-center gap-2">
            <p-button
                [style]="{'width':'101px','height':'38px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
                label="{{ 'back' | translate }}"
                (click)="onCancel(-1, '')"/>
            <p-button
                label="{{ 'next' | translate }}"
                [style]="{'width':'101px','height':'38px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                (click)="onSubmit(-1, '')"/>
        </div>
    </div>
</div>