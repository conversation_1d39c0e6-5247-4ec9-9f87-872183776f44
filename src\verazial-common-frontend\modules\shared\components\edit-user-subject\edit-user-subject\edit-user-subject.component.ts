import { Component, EventEmitter, Input, OnChang<PERSON>, OnD<PERSON>roy, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { ConfirmationService, MenuItem, MessageService, PrimeNGConfig } from 'primeng/api';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { RoleEntity } from 'src/verazial-common-frontend/core/general/common/entity/role.entity';
import { RoleType } from 'src/verazial-common-frontend/core/general/role/common/enum/role-type.enum';
import { GetAllRolesUseCase } from 'src/verazial-common-frontend/core/general/role/domain/use-cases/roles/get-all-roles.use-case';
import { StaticResourceEntity } from 'src/verazial-common-frontend/core/general/storage/domain/entity/static-resource.entity';
import { SubjectEntity } from 'src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity';
import { UserEntity } from 'src/verazial-common-frontend/core/general/user/domain/entity/user.entity';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { GenericKeyValue } from 'src/verazial-common-frontend/core/models/key-value.interface';
import { UserSubjectActionType } from 'src/verazial-common-frontend/core/models/user-subject-action-type.enum';
import { AuditTrailService } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { environment } from 'src/environments/environment';
import { ValidatorService } from '../../../services/validator.service';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { FileUploadHandlerEvent } from 'primeng/fileupload';

@Component({
  selector: 'app-edit-user-subject',
  templateUrl: './edit-user-subject.component.html',
  styleUrl: './edit-user-subject.component.css',
})
export class EditUserSubjectComponent implements OnInit, OnDestroy, OnChanges {
  @Input() readAndWritePermissions: boolean = false;
  @Input() userSubject: SubjectEntity | UserEntity | undefined;
  @Input() isUser: boolean = false;
  @Input() actionType: UserSubjectActionType | undefined;
  @Input() isVerified: boolean = false;
  @Input() userIsVerified: boolean = false;
  @Output() picture = new EventEmitter<{pic: string, uploaded: boolean}>;
  @Output() outputData = new EventEmitter<SubjectEntity | UserEntity>;
  @Output() allowSave = new EventEmitter<boolean>();
  @Output() allowDelete = new EventEmitter<boolean>();

  roles: RoleEntity[] = [];
  AllRoles: RoleEntity[] = [];
  currentUserRole: RoleEntity | undefined;
  selectedRoles: RoleEntity[] = [];

  profiles: RoleEntity[] = [];
  selectedProfiles: RoleEntity[] = [];

  showProfilePictureDialog: boolean = false;
  showSelectOptionDialog: boolean = false;
  showUploadDialog: boolean = false;
  selectedCurrentResult: StaticResourceEntity | undefined;
  imagePlaceholder: string = "verazial-common-frontend/assets/images/all/UserPic.svg";
  acceptedFiles: string = 'image/*';
  maxFileSize: string = '10400000'; // 10MB
  passwordNotMatch: boolean = false;
  emailNotValid: boolean = false;

  userSubjectGender: GenericKeyValue | undefined;
  userSubjectLanguage: GenericKeyValue | undefined;
  userSubjectDataSource: GenericKeyValue | undefined;
  userSubjectBirthdate: string = "";

  maxDate: Date | undefined;

  userSubjectData: SubjectEntity | UserEntity | undefined;

  profilePicOptions?: MenuItem[];

  isSelf: boolean = false;
  userIsTopRole: boolean = false;

  userSubjectActionTypes = UserSubjectActionType;

  confirmDialogTimeoutLimit: number = 0;
  startCheckingInactivity: boolean = false;

  genderOptions: GenericKeyValue[] = [
    { key: 'male', value: this.translate.instant('options.male') },
    { key: 'female', value: this.translate.instant('options.female') }
  ];

  languageOptions: GenericKeyValue[] = environment.languages.map(v => {
    return { key: v.code, value: this.translate.instant(v.label) }
  });

  apiDataSources?: any[];
  ldapDataSources?: any[];
  dataSourceOptions: GenericKeyValue[] = []
  allowSaveBasedOnDataSource: boolean = false;
  allowDeleteBasedOnDataSource: boolean = false;

  defaultMinLength = 12;
  minLenght = this.localStorageService.getSessionSettings()?.continued1?.passwordComplexity?.min ?? this.defaultMinLength;
  defaultRegex = /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/
  validatorPattern = this.localStorageService.getSessionSettings()?.continued1?.passwordComplexity?.regex != "" ? this.localStorageService.getSessionSettings()?.continued1?.passwordComplexity?.regex ?? this.defaultRegex : this.defaultRegex;

  public form: FormGroup = this.fb.group({
    numId: ['', [Validators.required]],
    names: ['', [Validators.required]],
    lastNames: ['', [Validators.required]],
    email: [''],
    roles: [[], [Validators.required]],
    rol: ['', [Validators.required]],
    birthdate: [],
    gender: [],
    language: [],
    datasource: [],
    password: [],
    repeatPassword: [],
    mustUpdate: [],
    subjectProfiles: [[]],
    mainProfile: [''],
  });

  filteredRoles: RoleEntity[] = [];
  filteredProfiles: RoleEntity[] = [];

  constructor(
    private fb: FormBuilder,
    private translate: TranslateService,
    private primengConfig: PrimeNGConfig,
    private messageService: MessageService,
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService,
    private auditTrailService: AuditTrailService,
    private validatorService: ValidatorService,
    private getAllRolesUseCase: GetAllRolesUseCase,
    private confirmationService: ConfirmationService,
    private checkPermissionsService: CheckPermissionsService,
  ) { }

  ngOnInit() {
    // this.loggerService.debug(this.actionType);
    const managerSettings = this.localStorageService.getSessionSettings();
    this.apiDataSources = managerSettings?.userSubjectApiExternalConnections?.map(v => {
      return { key: v.connectionId, value: v.connectionName, data: v }
    });
    if (this.apiDataSources) {
      const availableOptions = this.apiDataSources.filter(v => v.data?.isActive);
      if (availableOptions.length > 0) {
        this.dataSourceOptions = availableOptions.map(v => { return { key: v.key!, value: v.value! } });
      }
    }
    this.ldapDataSources = managerSettings?.userSubjectLdapConnections?.map(v => {
      return { key: v.connectionId, value: v.connectionName, data: v }
    });
    if (this.ldapDataSources) {
      const availableOptions = this.ldapDataSources.filter(v => v.data?.isActive);
      if (availableOptions.length > 0) {
        this.dataSourceOptions = [...this.dataSourceOptions, ...availableOptions.map(v => { return { key: v.key!, value: v.value! } })];
      }
    }
    this.checkPermisions(this.userSubject?.datasource);
    if (this.userSubject) this.userSubjectData = this.userSubject;
    if (this.isUser) {
      this.form.controls['email'].setValidators([Validators.required, Validators.email]);
      if (this.actionType == UserSubjectActionType.CREATE || this.isSelf) {
        this.minLenght = this.localStorageService.getSessionSettings()?.continued1?.passwordComplexity?.min ?? this.defaultMinLength;
        this.validatorPattern = this.localStorageService.getSessionSettings()?.continued1?.passwordComplexity?.regex != "" ? this.localStorageService.getSessionSettings()?.continued1?.passwordComplexity?.regex ?? this.defaultRegex : this.defaultRegex;
        this.form.controls['password'].setValidators([Validators.minLength(this.minLenght), Validators.pattern(this.validatorPattern)]);
        this.form.controls['repeatPassword'].setValidators([Validators.minLength(this.minLenght), Validators.pattern(this.validatorPattern)]);
      }
    }
    //Get the current user role
    this.currentUserRole = this.localStorageService.getRole() as RoleEntity;
    this.primengConfig.ripple = false;
    this.maxDate = new Date();
    this.getAllRoles();
    if (this.userSubject) {
      this.isSelf = this.userSubject.numId == this.localStorageService.getUser().numId;
      if (this.isSelf) {
        this.form.get('rol')?.disable();
        this.form.get('roles')?.disable();
      }
      this.loggerService.debug(this.userSubject);
      this.fillFields();
    }
    this.profilePicOptions = [
      {
        icon: 'pi pi-pencil',
        command: () => {
          this.openProfilePictureDialog();
        }
      },
      {
        icon: 'pi pi-trash',
        command: () => {
          this.confirmRemove()
        }
      },
    ];
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['userSubject']) {
      if (this.userSubject) {
        if(this.userSubjectData) this.userSubjectData!.id = this.userSubject.id;
        this.isSelf = this.userSubject.numId == this.localStorageService.getUser().numId;
        if (this.isSelf) {
          this.form.get('rol')?.disable();
          this.form.get('roles')?.disable();
        }
        this.fillFields();
      }
    }

    if (changes['isUser']) {
      this.updateRolValidation();
    }
  }

  ngOnDestroy() {
    // Clean up the timeout if the component is destroyed
    this.closeConfirmationDialog();
  }

  resetInactivityMonitor() {
    this.startCheckingInactivity = false;
    this.confirmDialogTimeoutLimit = 0;
  }

  private updateRolValidation(): void {
    if (this.isUser) {
      // User roles
      this.form.get('rol')?.clearValidators();
    } else {
      this.form.get('rol')?.setValidators([Validators.required]);
    }
    this.form.get('rol')?.updateValueAndValidity();
  }

  async getAllRoles() {
    await this.getAllRolesUseCase.execute().then(
      (roles_data) => {
        // this.loggerService.debug(roles_data);
        this.AllRoles = [...roles_data];
        if (this.isUser) {
          const data = roles_data.filter(v => v.type == RoleType.USER);
          const higherRoles = data.filter(v => v.level! < this.currentUserRole!.level!);
          const isSelf = this.userSubject?.numId == this.localStorageService.getUser().numId;
          if (higherRoles.length > 0 && !isSelf) {
            this.roles = [...data.filter(v => v.level! > this.currentUserRole!.level!)];
          }
          else {
            this.userIsTopRole = true;
            this.roles = [...data];
          }
          const dataProfiles = roles_data.filter(v => v.type == RoleType.SUBJECT).filter(v => v.name! != 'SYSTEM_USER');
          this.profiles = [...dataProfiles];
        }
        else {
          const data = roles_data.filter(v => v.type == RoleType.SUBJECT).filter(v => v.name! != 'SYSTEM_USER');
          // data.forEach(element => {
          //   if (element.name == 'SYSTEM_USER') {
          //     element.name = this.translate.instant('role_names.SYSTEM_USER');
          //   }
          // });
          this.roles = [...data];

        }
        if (this.userSubject) {
          this.loggerService.debug(this.userSubject);
          this.fillFields();
        }
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_ROLES, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  fillFields() {
    if (this.actionType == UserSubjectActionType.VIEWONLY || !this.readAndWritePermissions) {
      this.form.disable();
    }
    if (this.actionType == UserSubjectActionType.UPDATE) {
      this.form.get('numId')?.disable();
    }

    this.form.get('numId')?.setValue(this.userSubject?.numId);
    this.form.get('names')?.setValue(this.userSubject?.names);
    this.form.get('lastNames')?.setValue(this.userSubject?.lastNames);
    this.form.get('email')?.setValue(this.userSubject?.email);

    console.log(this.userSubject)
    if (this.userSubject?.roles && this.userSubject.roles.length > 0) {
      this.selectedRoles = this.userSubject.roles;
      if (this.roles.length != 0) {
        this.selectedRoles = this.roles.filter(v => this.userSubject?.roles?.find(r => r.id == v.id));
      }
      this.form.get('roles')?.setValue(this.selectedRoles);

      if(this.userSubject.defaultRole != undefined) {
        var defaultRole = this.selectedRoles.find(v => v.id?.toString() == this.userSubject?.defaultRole?.toString());
        this.form.get('rol')?.setValue(defaultRole);
      }
    }

    if (this.userSubject?.birthdate) {
      this.form.get('birthdate')?.setValue(this.userSubject.birthdate);
    }

    if(this.userSubject?.mustUpdatePassword) {
      this.form.get('mustUpdate')?.setValue(this.userSubject.mustUpdatePassword);
    }
    this.userSubjectGender = this.genderOptions.find(v => v.key == this.userSubject?.gender?.toLowerCase());
    this.userSubjectLanguage = this.languageOptions.find(v => v.key == this.userSubject?.language?.toLowerCase());
    this.userSubjectDataSource = this.dataSourceOptions.find(v => v.key == this.userSubject?.datasource);
  }

  isValid(field: string): boolean {
    return this.validatorService.isValidField(this.form, field);
  }

  isRequiredField(field: string): boolean {
    return this.validatorService.isRequiredField(this.form, field);
  }

  checkSpecificError(field: string, error: string): boolean {
    if (error == 'minLength') {
      let result = true;
      let control = this.form.controls[field]
      if(control.errors) {
        const object = control.errors['minlength'];
        if (object) {
          result = object.requiredLength > object.actualLength;
        }
        else {
          result = false;
        }
      }
      return result;
    }
    return this.validatorService.checkSpecificError(this.form, field, error);
  }

  trackDataChange() {
    if (!this.userSubjectData) {
      this.userSubjectData = this.isUser ? new UserEntity() : new SubjectEntity();
    }

    if (this.isUser) {
      this.form.get('email')?.markAsTouched;
      if (!this.isValid('email')) {
        this.emailNotValid = true;
        return;
      }
      else {
        this.emailNotValid = false;
      }
      if (this.form.get('password')?.value != null && this.form.get('repeatPassword')?.value != null) {
        if (this.form.get('password')?.value != this.form.get('repeatPassword')?.value) {
          this.form.get('password')?.markAsTouched;
          this.form.get('repeatPassword')?.markAsTouched;
          this.passwordNotMatch = true;
          return
        }
        else {
          this.passwordNotMatch = false;
        }
      } else {
        this.passwordNotMatch = false;
        this.userSubjectData.password = undefined;
      }
      if (this.form.get('password')?.value != null && this.form.get('password')?.value != undefined && this.form.get('password')?.value != '') {
        this.userSubjectData.password = this.form.get('password')?.value;
      }
      this.userSubjectData.mustUpdatePassword = this.form.get('mustUpdate')?.value;
    }

    this.userSubjectData.numId = this.form.get('numId')?.value;
    this.userSubjectData.names = this.form.get('names')?.value;
    this.userSubjectData.lastNames = this.form.get('lastNames')?.value;
    this.userSubjectData.email = this.form.get('email')?.value;
    this.userSubjectData.roles = this.form.get('roles')?.value;
    if(this.form.get('rol')?.value) {
      //console.log(this.form.get('rol')?.value);
      this.userSubjectData.defaultRole = this.form.get('rol')?.value.id.toString();
    }
    else {
      this.userSubjectData.defaultRole = undefined;
    }
    this.userSubjectData.birthdate = this.form.get('birthdate')?.value;
    if (this.form.get('gender')?.touched && this.form.get('gender')?.value) {
      this.userSubjectData.gender = this.form.get('gender')?.value.key;
    }
    if (this.form.get('language')?.touched && this.form.get('language')?.value) {
      this.userSubjectData.language = this.form.get('language')?.value.key;
    }
    else if (this.userSubjectLanguage == undefined) {
      this.userSubjectData.language = this.localStorageService.getSessionSettings()?.defaultLanguage ?? this.localStorageService.getLanguage() ?? environment.defaultLanguage;
    }
    if (this.form.get('datasource')?.touched && this.form.get('datasource')?.value) {
      this.userSubjectData.datasource = this.form.get('datasource')?.value.key;
    }
    this.checkPermisions(this.userSubjectData.datasource);
    if (this.form.get('subjectProfiles')?.touched && this.form.get('subjectProfiles')?.value) {
      this.userSubjectData.subjectProfiles = this.form.get('subjectProfiles')?.value;
    }
    if (this.form.get('mainProfile')?.touched && this.form.get('mainProfile')?.value) {
      this.userSubjectData.mainProfile = this.form.get('mainProfile')?.value;
    }

    this.outputData.emit(this.userSubjectData);
  }

  checkPermisions(dataSourceId?: string) {
    const dataSources = [... this.apiDataSources!, ... this.ldapDataSources!];
    if (dataSources.length > 0 && dataSourceId != '' && dataSourceId != undefined && dataSourceId != null) {
      const userSubjectDataSource = dataSources.find(v => v.key == dataSourceId);
      if (userSubjectDataSource) {
        this.allowSaveBasedOnDataSource = this.actionType == UserSubjectActionType.CREATE ? userSubjectDataSource.data.allowCreating : (this.actionType == UserSubjectActionType.UPDATE ? userSubjectDataSource.data.allowUpdating : (this.actionType == UserSubjectActionType.VIEWONLY ? userSubjectDataSource.data.allowReading : false));
        this.allowDeleteBasedOnDataSource = userSubjectDataSource.data.allowDeleting;
      }
      else {
        this.allowSaveBasedOnDataSource = true;
        this.allowDeleteBasedOnDataSource = true;
      }
    }
    else {
      this.allowSaveBasedOnDataSource = true;
      this.allowDeleteBasedOnDataSource = true;
    }
    this.allowSave.emit(this.allowSaveBasedOnDataSource);
    this.allowDelete.emit(this.allowDeleteBasedOnDataSource);
  }

  async updatePic(pic: string, uploaded: boolean = false): Promise<void> {
    if (!this.userSubjectData) {
      this.userSubjectData = this.isUser ? new UserEntity() : new SubjectEntity();
    }
    this.userSubjectData.pic = pic;
    this.trackDataChange();
    this.picture.emit({pic, uploaded});
    this.closeDialog();
  }

  disableCopyPaste(event: ClipboardEvent): void {
    event.preventDefault();
  }
  /* Camera Functions */

  onCameraResult(event: { action: string, staticResource: StaticResourceEntity } | any) {
    // this.loggerService.debug(event);
    switch (event.action) {
      case 'close':
        this.closeDialog();
        break;
      case 'delete':
        this.updatePic(this.imagePlaceholder);
        break;
      case 'create':
        this.updatePic('data:image/jpeg;base64,' + event.staticResource?.content);
        break;
    }
  }

  confirmRemove() {
    this.confirmationService.confirm({
      message: this.translate.instant('messages.confirm_delete_profile_picture'),
      header: this.translate.instant('messages.delete_confirmation_header'),
      icon: 'pi pi-exclamation-triangle',
      rejectButtonStyleClass: "p-button-text",
      acceptButtonStyleClass: "ng-confirm-button",
      acceptIcon: "none",
      rejectIcon: "none",
      acceptLabel: this.translate.instant("delete"),
      rejectLabel: this.translate.instant("no"),
      accept: () => {
        this.updatePic(this.imagePlaceholder);
      },
      reject: () => {
        this.resetInactivityMonitor();
      }
    });

    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }

  openProfilePictureDialog() {
    const enrollByFiles = (this.localStorageService.getSessionSettings()?.continued1?.criminalisticsSettings?.isCriminalisticsEnabled ?? false) &&
                        (this.localStorageService.getSessionSettings()?.continued1?.criminalisticsSettings?.isEnrollByFileEnabled ?? false) &&
                        this.checkPermissionsService.hasReadAndWritePermissions(AccessIdentifier.ENROLL_BY_FILE);
    if (enrollByFiles) {
      this.showSelectOptionDialog = true;
    }
    else {
      this.openCamera();
    }
  }

  openCamera() {
    this.showSelectOptionDialog = false;
    this.selectedCurrentResult = new StaticResourceEntity();
    let image = this.userSubject?.pic?.replace('data:image/jpeg;base64,', '');
    this.selectedCurrentResult.id = image ? 'id' : undefined;
    this.selectedCurrentResult.content = "";
    this.showProfilePictureDialog = true;
  }

  openFileUpload() {
    this.showSelectOptionDialog = false;
    this.showUploadDialog = true;
  }

  onUploadResult(result: {event: FileUploadHandlerEvent, file: StaticResourceEntity}) {
    this.loggerService.debug("Uploaded file");
    this.loggerService.debug(result);
    const file = result.event.files[0];
    const reader = new FileReader();

    reader.onload = () => {
      // The result is a Base64 string with a prefix
      let content = (reader.result as string).replace('data:' + file.type + ';base64,', '');

      this.updatePic('data:image/jpeg;base64,' + content, true);
      this.showUploadDialog = false;
    };
    reader.onerror = (error) => {
      this.loggerService.error('Error reading file:');
      this.loggerService.error(error);
      this.messageService.add({
        severity: 'error',
        summary: this.translate.instant('content.errorTitle'),
        detail: this.translate.instant('messages.fileUploadedError').replace('{0}', file.name),
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });
    };

    // Read the file as a Data URL
    reader.readAsDataURL(file);
  }

  onCancelUpload() {
    this.showUploadDialog = false;
  }

  async closeDialog() {
    this.selectedCurrentResult = undefined;
    this.showProfilePictureDialog = false;
  }

  closeConfirmationDialog() {
    this.confirmationService.close();
    this.resetInactivityMonitor();
  }

  updatePrincipalRoles() {
    const selectedRoles: RoleEntity[] = this.form.get('roles')?.value || [];
    this.filteredRoles = selectedRoles;

    const currentPrincipal = this.form.get('rol')?.value;
    if (!selectedRoles.some(role => role.name === currentPrincipal?.name)) {
        this.form.get('rol')?.setValue(null);
    }

    this.trackDataChange();
  }

  updatePrincipalProfiles() {
    const selectedProfiles: RoleEntity[] = this.form.get('subjectProfiles')?.value || [];
    this.filteredProfiles = selectedProfiles;

    const currentPrincipal = this.form.get('mainProfile')?.value;
    if (!selectedProfiles.some(role => role.name === currentPrincipal?.name)) {
        this.form.get('mainProfile')?.setValue(null);
    }

    this.trackDataChange();
  }
}