.example-component {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.header {
  margin-bottom: 30px;
  text-align: center;
}

.header h2 {
  color: #204887;
  margin-bottom: 10px;
}

.header p {
  color: #666;
  font-style: italic;
}

.actions-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.actions-section h3 {
  margin-bottom: 15px;
  color: #204887;
}

.button-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.button-group button {
  min-width: 120px;
}

.info-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #d4edda;
  border-radius: 8px;
  background-color: #d1ecf1;
}

.info-section h3 {
  margin-bottom: 15px;
  color: #0c5460;
}

.info-section ul {
  margin: 0;
  padding-left: 20px;
}

.info-section li {
  margin-bottom: 8px;
  color: #0c5460;
}

.status-section {
  padding: 20px;
  border: 1px solid #f0ad4e;
  border-radius: 8px;
  background-color: #fcf8e3;
}

.status-section h3 {
  margin-bottom: 15px;
  color: #8a6d3b;
}

.status-section p {
  margin-bottom: 8px;
  color: #8a6d3b;
}

.status-section strong {
  font-weight: 600;
}
