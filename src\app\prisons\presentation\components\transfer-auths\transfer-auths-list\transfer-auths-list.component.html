<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<p-toast></p-toast>
<p-confirmDialog [style]="{width: '395px'}"></p-confirmDialog>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<div class="subcontainer">
    <div *ngIf="listOfTransferAuth.length != 0 else empty" class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ 'titles.transfer_authorization' | translate}}</label>
                </div>
                <div class="tableNumSelectedRowsText flex flex-row align-items-center px-3 border-x-1 border-300">
                    @if(selectedTransferAuths.length > 0){
                        <div>
                            {{ selectedTransferAuths.length + ' ' + ('content.selected' | translate) }}
                        </div>
                        <button pButton [disabled]="!canReadAndWrite && !readOnly || selectedTransferAuths.length > 1" icon="pi pi-{{ readOnly || !userIsVerified ? 'eye' : 'pencil' }}" [text]="true" class="ml-3" style="padding: 0; width: 1.5rem;" (click)="onEditTransferAuth()"></button>
                        <button pButton [disabled]="!canReadAndWrite || !userIsVerified" icon="pi pi-trash" [text]="true" class="ml-2" style="padding: 0; width: 1.5rem;" (click)="deleteMultipleTransferAuths()"></button>
                    }
                </div>
            </div>
            <div class="flex flex-row flex-wrap justify-content-center gap-4 align-items-center">
                <p-iconField iconPosition="right">
                    <input pInputText type="text"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="{{ 'content.search' | translate }}"
                    />
                    <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                </p-iconField>
                <div class="add-action-main-full">
                    <p-button
                        ngClass="add-action-main-full"
                        [disabled]="!(canReadAndWrite && userIsVerified)"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'content.new_transfer' | translate }}"
                        icon="pi pi-plus" iconPos="right"
                        [rounded]="true"
                        (onClick)="onNewTransferAuth()"
                    ></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button
                        [disabled]="!(canReadAndWrite && userIsVerified)"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        icon="pi pi-plus"
                        [rounded]="true"
                        (onClick)="onNewTransferAuth()"
                    ></p-button>
                </div>
            </div>
        </div>
        <div></div>
        <p-table
            #dt
            [value]="listOfTransferAuth"
            (onFilter)="onFilter($event, dt)"
            [(selection)]="selectedTransferAuths"
            dataKey="id"
            [rowHover]="true"
            [paginator]="true"
            [rows]="10"
            [rowsPerPageOptions]="[5, 10, 20]"
            [scrollable]="true"
            scrollDirection="horizontal"
            [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="[
                'id',
                'authReason',
                'authRegistrationDate',
                'authUserSignatureDate',
                'authUserId',
                'authExpirationDate',
                'originLocationId',
                'destinyLocationId',
                'plannedDepartureDateTime',
                'plannedArrivalDateTime',
                'status',
                'isCompleted',
                'isCancelled',
                'cancelUserSignatureDate',
                'cancelUserId',
                'createdBy',
                'createdAt',
                'updatedBy',
                'updatedAt',
            ]"
            [sortField]="'id'" [sortOrder]="1">
            <ng-template pTemplate="header">
                <tr>
                    <th style="width: 4rem"></th>
                    <th class="fixed-column" pSortableColumn="id"> {{ 'content.authCode' | translate }} <p-sortIcon field="id"></p-sortIcon></th>
                    <!-- <th class="fixed-column" pSortableColumn="authCode"> {{ 'content.authCode' | translate }} <p-sortIcon field="authCode"></p-sortIcon></th> -->
                    <th class="fixed-column" pSortableColumn="authReason"> {{ 'content.authReason' | translate }} <p-sortIcon field="authReason"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="authRegistrationDate"> {{ 'content.authRegistrationDate' | translate }} <p-sortIcon field="authRegistrationDate"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="authUserSignatureDate"> {{ 'content.authDate' | translate }} <p-sortIcon field="authUserSignatureDate"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="authUserId"> {{ 'content.authUser' | translate }} <p-sortIcon field="authUserId"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="authExpirationDate"> {{ 'content.authExpirationDate' | translate }} <p-sortIcon field="authExpirationDate"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="originLocationId"> {{ 'content.originLocation' | translate }} <p-sortIcon field="originLocationId"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="destinyLocationId"> {{ 'content.destinyLocation' | translate }} <p-sortIcon field="destinyLocationId"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="plannedDepartureDateTime"> {{ 'content.plannedDepartureDateTime' | translate }} <p-sortIcon field="plannedDepartureDateTime"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="actualDepartureDateTime"> {{ 'content.actualDepartureDateTime' | translate }} <p-sortIcon field="actualDepartureDateTime"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="plannedArrivalDateTime"> {{ 'content.plannedArrivalDateTime' | translate }} <p-sortIcon field="plannedArrivalDateTime"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="actualArrivalDateTime"> {{ 'content.actualArrivalDateTime' | translate }} <p-sortIcon field="actualArrivalDateTime"></p-sortIcon></th>
                    <!-- <th class="fixed-column" pSortableColumn="details"> {{ 'content.details' | translate }}</th>
                    <th class="fixed-column" pSortableColumn="listOfPrisoners"> {{ 'content.listOfPrisoners' | translate }}</th>
                    <th class="fixed-column" pSortableColumn="listOfResponsibleUsers"> {{ 'content.listOfResponsiblePersonel' | translate }}</th> -->
                    <th class="fixed-column" pSortableColumn="status"> {{ 'content.status' | translate }} <p-sortIcon field="status"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="isCompleted"> {{ 'content.isCompleted' | translate }} <p-sortIcon field="isCompleted"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="isCancelled"> {{ 'content.isCancelled' | translate }} <p-sortIcon field="isCancelled"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="cancelUserSignatureDate"> {{ 'content.cancelDate' | translate }} <p-sortIcon field="cancelUserSignatureDate"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="cancelUserId"> {{ 'content.cancelUser' | translate }} <p-sortIcon field="cancelUserId"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="createdBy"> {{ 'content.createdBy' | translate }} <p-sortIcon field="createdBy"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="createdAt"> {{ 'content.createdAt' | translate }} <p-sortIcon field="createdAt"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="updatedBy"> {{ 'content.updatedBy' | translate }} <p-sortIcon field="updatedBy"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="updatedAt"> {{ 'content.updatedAt' | translate }} <p-sortIcon field="updatedAt"></p-sortIcon></th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th style="width: 4rem">
                        <p-tableHeaderCheckbox/>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="id" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <!-- <th>
                        <p-columnFilter type="text" field="authCode" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th> -->
                    <th>
                        <p-columnFilter type="text" field="authReason" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="authRegistrationDate" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'authRegistrationDate')"
                                    (onInput)="applyDateRangeFilter(dt, 'authRegistrationDate')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'authRegistrationDate')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="authUserSignatureDate" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'authUserSignatureDate')"
                                    (onInput)="applyDateRangeFilter(dt, 'authUserSignatureDate')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'authUserSignatureDate')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="authUserId" [showMenu]="false" matchMode="subjectNames">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="authExpirationDate" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'authExpirationDate')"
                                    (onInput)="applyDateRangeFilter(dt, 'authExpirationDate')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'authExpirationDate')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="originLocationId" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-treeSelect appendTo="body"
                                    class="md:w-20rem w-full"
                                    containerStyleClass="w-full"
                                    [options]="listLocations"
                                    [ngModel]="value"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    [filter]="true"
                                    (onNodeSelect)="filter($event.node.key)"
                                    (onClear)="filter(null)"
                                ></p-treeSelect>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="destinyLocationId" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-treeSelect appendTo="body"
                                    class="md:w-20rem w-full"
                                    containerStyleClass="w-full"
                                    [options]="listLocationsDestiny"
                                    [ngModel]="value"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    [filter]="true"
                                    (onNodeSelect)="filter($event.node.key)"
                                    (onClear)="filter(null)"
                                ></p-treeSelect>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="plannedDepartureDateTime" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'plannedDepartureDateTime')"
                                    (onInput)="applyDateRangeFilter(dt, 'plannedDepartureDateTime')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'plannedDepartureDateTime')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="actualDepartureDateTime" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'actualDepartureDateTime')"
                                    (onInput)="applyDateRangeFilter(dt, 'actualDepartureDateTime')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'actualDepartureDateTime')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="plannedArrivalDateTime" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'plannedArrivalDateTime')"
                                    (onInput)="applyDateRangeFilter(dt, 'plannedArrivalDateTime')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'plannedArrivalDateTime')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="actualArrivalDateTime" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'actualArrivalDateTime')"
                                    (onInput)="applyDateRangeFilter(dt, 'actualArrivalDateTime')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'actualArrivalDateTime')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <!-- <th>
                        <p-columnFilter type="text" field="details" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="listOfPrisoners" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="listOfResponsibleUsers" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th> -->
                    <th>
                        <p-columnFilter type="text" field="status" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="isCompleted" matchMode="equals" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown
                                    appendTo="body"
                                    [ngModel]="value"
                                    [options]="[
                                        { label: 'options.true' | translate, value: true },
                                        { label: 'options.false' | translate, value: false }
                                    ]"
                                    (onChange)="filter($event.value?.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="label"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ value !== undefined ? (value ? ('options.true' | translate) : ('options.false' | translate)) : '' }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.label }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="isCancelled" matchMode="equals" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown
                                    appendTo="body"
                                    [ngModel]="value"
                                    [options]="[
                                        { label: 'options.true' | translate, value: true },
                                        { label: 'options.false' | translate, value: false }
                                    ]"
                                    (onChange)="filter($event.value?.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="label"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ value !== undefined ? (value ? ('options.true' | translate) : ('options.false' | translate)) : '' }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.label }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="cancelUserSignatureDate" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'cancelUserSignatureDate')"
                                    (onInput)="applyDateRangeFilter(dt, 'cancelUserSignatureDate')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'cancelUserSignatureDate')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="cancelUserId" [showMenu]="false" matchMode="subjectNames">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="createdBy" [showMenu]="false" matchMode="userNames">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="createdAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'createdAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="updatedBy" [showMenu]="false" matchMode="userNames">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="updatedAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'updatedAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-auth let-rowIndex="rowIndex">
                <tr *ngIf="getCanReadAndWriteAuth(auth)" class="p-selectable-row" [pSelectableRow]="auth" [pSelectableRowIndex]="rowIndex">
                    <td>
                        <p-tableCheckbox [value]="auth"></p-tableCheckbox>
                    </td>
                    <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.id}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.id }}</td>
                    <!-- <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.authCode}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authCode }}</td> -->
                    <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.authReason}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authReason }}</td>
                    @if(isValidDate(auth.authRegistrationDate)){
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.authRegistrationDate?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authRegistrationDate.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.authRegistrationDate}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authRegistrationDate }}</td> -->
                    @if(isValidDate(auth.authUserSignatureDate)){
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.authUserSignatureDate?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authUserSignatureDate.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.authUserSignatureDate}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authUserSignatureDate }}</td> -->
                    <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{getSubjectNamesById(auth.authUserId)}}" tooltipPosition="top" class="ellipsis-cell">{{ getSubjectNamesById(auth.authUserId) }}</td>
                    @if(isValidDate(auth.authExpirationDate)){
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.authExpirationDate?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authExpirationDate.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.authExpirationDate}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authExpirationDate }}</td> -->
                    <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{getLocationPath(auth.originLocationId)}}" tooltipPosition="top" class="ellipsis-cell">{{ getLocationPath(auth.originLocationId) }}</td>
                    <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{getLocationPath(auth.destinyLocationId)}}" tooltipPosition="top" class="ellipsis-cell">{{ getLocationPath(auth.destinyLocationId) }}</td>
                    @if(isValidDate(auth.plannedDepartureDateTime)){
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.plannedDepartureDateTime?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.plannedDepartureDateTime.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    @if(isValidDate(auth.actualDepartureDateTime)){
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.actualDepartureDateTime?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.actualDepartureDateTime.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    @if(isValidDate(auth.plannedArrivalDateTime)){
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.plannedArrivalDateTime?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.plannedArrivalDateTime.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    @if(isValidDate(auth.actualArrivalDateTime)){
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.actualArrivalDateTime?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.actualArrivalDateTime.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.details}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.details }}</td>
                    <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.listOfPrisoners}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.listOfPrisoners }}</td>
                    <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.listOfResponsibleUsers}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.listOfResponsibleUsers }}</td> -->
                    <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.status ? ('status.' + auth.status | translate) : ''}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.status ? ('status.' + auth.status | translate) : '' }}</td>
                    <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{'options.' + auth.isCompleted | translate}}" tooltipPosition="top" class="ellipsis-cell">{{ 'options.' + auth.isCompleted | translate }}</td>
                    <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{'options.' + auth.isCancelled | translate}}" tooltipPosition="top" class="ellipsis-cell">{{ 'options.' + auth.isCancelled | translate }}</td>
                    @if(isValidDate(auth.cancelUserSignatureDate)){
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.cancelUserSignatureDate?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.cancelUserSignatureDate.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.cancelUserSignatureDate}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.cancelUserSignatureDate }}</td> -->
                    <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{getSubjectNamesById(auth.cancelUserId)}}" tooltipPosition="top" class="ellipsis-cell">{{ getSubjectNamesById(auth.cancelUserId) }}</td>
                    <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{getUserNamesById(auth.createdBy)}}" tooltipPosition="top" class="ellipsis-cell">{{ getUserNamesById(auth.createdBy) }}</td>
                    @if(isValidDate(auth.createdAt)){
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.createdAt?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.createdAt.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.createdAt}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.createdAt }}</td> -->
                    <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{getUserNamesById(auth.updatedBy)}}" tooltipPosition="top" class="ellipsis-cell">{{ getUserNamesById(auth.updatedBy) }}</td>
                    @if(isValidDate(auth.updatedAt)){
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.updatedAt?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.updatedAt.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="onEditTransferAuth(auth)" showDelay="1000" pTooltip="{{auth.updatedAt}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.updatedAt }}</td> -->
                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                        <div class="flex flex-row">
                            <button pButton pRipple [disabled]="!canReadAndWrite && !readOnly" icon="pi pi-{{ readOnly || !userIsVerified || !enableEdit(auth) ? 'eye' : 'pencil' }}" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="onEditTransferAuth(auth)"></button>
                            <button *ngIf="auth.cancelUserId == '' && auth.authUserId != '' && (auth.status === AuthStatus.IN_PROGRESS || auth.status === AuthStatus.AUTHORIZED)"
                                pButton pRipple
                                [disabled]="!canReadAndWrite && !readOnly"
                                icon="pi pi-truck"
                                [text]="true"
                                class="mr-2"
                                style="padding: 0; width: 1.5rem;"
                                (click)="onExecuteTransferAuth(auth)"
                                [ngStyle]="{'transform': auth.status === AuthStatus.IN_PROGRESS ? 'scaleX(-1)' : 'none'}"
                                pTooltip="{{ auth.status === AuthStatus.IN_PROGRESS ? ('content.execute_transfer_entry' | translate) : ('content.execute_transfer_exit' | translate) }}"
                                tooltipPosition="top">
                            </button>
                            <button *ngIf="auth.status === AuthStatus.CREATED || auth.status === AuthStatus.EXPIRED" pButton pRipple [disabled]="!canReadAndWrite || !userIsVerified" icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="deleteTransferAuth(auth)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>

<p-dialog [(visible)]="showTransferAuthDialog" styleClass="p-fluid" [style]="{'width': '70vw'}" [closable]="true" [modal]="true">
    <ng-template pTemplate="header">
        <div></div>
        <div class="dialog-title">
            {{ (operationType == opType.INSERT ? 'content.new_transfer_auth' : 'content.edit_transfer_auth') | translate }}
        </div>
    </ng-template>
    <ng-template pTemplate="content">
        <app-transfer-auth-edit
            [canReadAndWrite]="canReadAndWriteAuth"
            [userIsVerified]="userIsVerified"
            [operationType]="operationType"
            [transferAuth]="transferAuth"
            [listLocations]="listLocations"
            [listLocationsDestiny]="listLocationsDestiny"
            [listOfAllSubjects]="listOfAllSubjects"
            [listOfAllRoles]="listOfAllRoles"
            [managerSettings]="managerSettings"
            [createUpdateButtonTitle]="createUpdateButtonTitle"
            (operationStatus)="operationStatus($event)"
        ></app-transfer-auth-edit>
    </ng-template>
</p-dialog>


<p-dialog [(visible)]="showExecuteTransferAuthDialog" styleClass="p-fluid" [style]="{'width': '70vw'}" [closable]="true" [modal]="true">
    <ng-template pTemplate="header">
        <div></div>
        <div *ngIf="transferAuth.status === AuthStatus.AUTHORIZED" class="dialog-title">
            {{ 'content.execute_transfer_exit' | translate }}
        </div>
        <div *ngIf="transferAuth.status === AuthStatus.IN_PROGRESS" class="dialog-title">
            {{ 'content.execute_transfer_entry' | translate }}
        </div>
    </ng-template>
    <ng-template pTemplate="content">
        <app-transfer-auth-execute
            [canReadAndWrite]="canReadAndWriteAuth"
            [userIsVerified]="userIsVerified"
            [transferAuth]="transferAuth"
            [listLocations]="listLocations"
            [listLocationsDestiny]="listLocationsDestiny"
            [listOfAllRoles]="listOfAllRoles"
            [listOfAllSubjects]="listOfAllSubjects"
            [managerSettings]="managerSettings"
            (operationStatus)="operationStatusExecute($event)"
        ></app-transfer-auth-execute>
    </ng-template>
</p-dialog>

<ng-template #empty>
    <div class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ 'titles.transfer_authorization' | translate}}</label>
                </div>
            </div>
        </div>
    </div>
    <div style="height: 65vh;" class="flex justify-content-center align-items-center">
        <app-empty
            [readAndWritePermissions]="canReadAndWrite && userIsVerified"
            buttonLabel="content.new_transfer"
            titleLabel="titles.no_transfer_auths_available"
            contentHeight="300px"
            (clicked)="onNewTransferAuth()">
        </app-empty>
    </div>
</ng-template>