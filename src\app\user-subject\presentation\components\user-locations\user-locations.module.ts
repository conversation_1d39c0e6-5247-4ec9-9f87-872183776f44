import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TranslateModule } from "@ngx-translate/core";
import { ProgressSpinnerModule } from "primeng/progressspinner";
import { DialogModule } from "primeng/dialog";
import { ButtonModule } from "primeng/button";
import { TableModule } from "primeng/table";
import { IconFieldModule } from "primeng/iconfield";
import { InputTextModule } from "primeng/inputtext";
import { InputIconModule } from "primeng/inputicon";
import { ConfirmDialogModule } from "primeng/confirmdialog";
import { DropdownModule } from "primeng/dropdown";
import { InputSwitchModule } from "primeng/inputswitch";
import { UserLocationsComponent } from "./user-locations/user-locations.component";
import { CalendarModule } from "primeng/calendar";
import { TreeSelectModule } from 'primeng/treeselect';
import { TooltipModule } from "primeng/tooltip";
import { InactivityMonitorModule } from "src/verazial-common-frontend/modules/shared/components/inactivity-monitor/inactivity-monitor.module";

@NgModule({
    declarations: [
      UserLocationsComponent
    ],
    imports: [
      /* Angular Modules */
      CommonModule,
      /* Forms */
      ReactiveFormsModule,
      FormsModule,
      /* Translate */
      TranslateModule,
      /* PrimeNG Modules */
      ProgressSpinnerModule,
      DialogModule,
      ButtonModule,
      TableModule,
      IconFieldModule,
      InputIconModule,
      InputTextModule,
      ConfirmDialogModule,
      DropdownModule,
      InputSwitchModule,
      CalendarModule,
      TreeSelectModule,
      TooltipModule,
      /* Custom Modules */
      InactivityMonitorModule,
    ],
    exports: [
      UserLocationsComponent
    ]
  })
  export class UserLocationsModule { }