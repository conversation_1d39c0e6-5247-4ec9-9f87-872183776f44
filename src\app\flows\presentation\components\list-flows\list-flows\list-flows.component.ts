import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { MessageService, ConfirmationService, FilterService } from 'primeng/api';
import { Table } from 'primeng/table';
import { TaskFlowEntity } from 'src/verazial-common-frontend/core/general/flow/domain/entity/task-flow.entity';
import { DeleteDrawFlowByTaskFlowIdUseCase } from 'src/verazial-common-frontend/core/general/flow/domain/use-cases/draw-flow/delete-draw-flow-by-task-flow-id.use-case';
import { DeleteTaskFlowByIdUseCase } from 'src/verazial-common-frontend/core/general/flow/domain/use-cases/task-flow/delete-task-flow-by-id.use-case';
import { GetTaskFlowByIdUseCase } from 'src/verazial-common-frontend/core/general/flow/domain/use-cases/task-flow/get-task-flow-by-id.use-case';
import { UpdateTaskFlowUseCase } from 'src/verazial-common-frontend/core/general/flow/domain/use-cases/task-flow/update-task-flow.use-case';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'app-list-flows',
  templateUrl: './list-flows.component.html',
  styleUrl: './list-flows.component.css',
  providers: [MessageService, ConfirmationService]
})
export class ListFlowsComponent implements OnInit, OnDestroy {

  // Inputs
  @Input() data: TaskFlowEntity[] = [];
  @Input() readAndWritePermissions: boolean = false;
  // Outputs
  @Output() onNewDataFlow = new EventEmitter<boolean>();
  @Output() onEdit = new EventEmitter<TaskFlowEntity>();

  selectedData: any;
  searchValue: string | undefined;

  selectedFlow: TaskFlowEntity | null = null;

  // Date Range Filter
  formGroup: FormGroup = new FormGroup({
    date: new FormControl<Date[] | null>(null)
  });
  dateFilterValues = {
    startDate: null,
    endDate: null
  };
  rangeDates: Date[] | null = null;

  confirmDialogTimeoutLimit: number = 0;
  startCheckingInactivity: boolean = false;

  showUpdateTaskFlowDialog: boolean = false;

  constructor(
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private translate: TranslateService,
    private deleteDrawFlowByTaskFlowIdUseCase: DeleteDrawFlowByTaskFlowIdUseCase,
    private deleteTaskFlowByIdUseCase: DeleteTaskFlowByIdUseCase,
    private getTaskFlowByIdUseCase: GetTaskFlowByIdUseCase,
    private updateTaskFlowUseCase: UpdateTaskFlowUseCase,
    private localStorageService: LocalStorageService,
    private consoleLogger: ConsoleLoggerService,
    private filterService: FilterService,
    private fb: FormBuilder,
    private auditTrailService: AuditTrailService,
  ){
    this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
      if (!filter || (!filter.startDate && !filter.endDate)) {
        return true; // If no filter, show all
      }
      const dateValue = new Date(value).getTime();
      const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
      const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
      if (startDate && endDate) {
        return dateValue >= startDate && dateValue <= endDate;
      } else if (startDate) {
        return dateValue >= startDate;
      } else if (endDate) {
        return dateValue <= endDate;
      }
      return false;
    });
  }

  public form: FormGroup = this.fb.group({
    isPublished: [],
    flowName: [],
    flowDescription: []
});

  ngOnInit(): void { }

  ngOnDestroy() {
    // Clean up the timeout if the component is destroyed
    this.closeConfirmationDialog();
  }

  resetInactivityMonitor() {
    this.startCheckingInactivity = false;
    this.confirmDialogTimeoutLimit = 0;
  }

  getSeverity(status: boolean) {
    switch (status) {
      case true: {
        return '#F59E0B';
        // return 'success';
      }
      case false: {
        return '#64748B';
        // return 'info';
      }
      default: {
        return 'var(--primary-color)';
      }
    }
  }

  deleteFlow(data: TaskFlowEntity) {
    this.confirmationService.confirm({
        message: this.translate.instant('messages.message_update') + " <b>" + data.name + '</b>?',
        header: this.translate.instant('flow.remove_flow'),
        icon: 'pi pi-exclamation-triangle',
        acceptIcon:"none",
        rejectIcon:"none",
        rejectButtonStyleClass:"p-button-text",
        acceptButtonStyleClass:"ng-confirm-button",
        acceptLabel: this.translate.instant('delete'),
        rejectLabel: this.translate.instant('no'),
        accept: () => {
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
          ];
          this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.DEL_FLW, ReasonActionTypeEnum.DELETE, () => {
            this.deleteDrawFlowByTaskFlowById(data);
            this.deleteTaskFlowById(data);
           }, at_attributes);
        },
        reject: () => {
        this.resetInactivityMonitor();
      }
    });

    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }

  deleteTaskFlowById(dataFlow: TaskFlowEntity){
    this.deleteTaskFlowByIdUseCase.execute({id: dataFlow.id!!}).then(
      (data) => {
        this.consoleLogger.debug(data);
        this.data = [...this.data.filter((v,_)=> v.id != dataFlow.id )];
        this.messageService.add({severity:'success', summary: this.translate.instant("titles.success_operation"), detail:this.translate.instant("messages.success_general"),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000});
      },
      (e) => {
        this.consoleLogger.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.RECORD_ID, value: dataFlow.id?.toString() },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(dataFlow) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_FLW, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  deleteDrawFlowByTaskFlowById(dataFlow: TaskFlowEntity){
    this.deleteDrawFlowByTaskFlowIdUseCase.execute({taskFlowId: dataFlow.id!!}).then(
      (data) => {
        this.consoleLogger.debug(data);
      },
      (e) => {
        this.consoleLogger.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.RECORD_ID, value: dataFlow.id?.toString() },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(dataFlow) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_FLW, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  editFlow(data: any){
    this.onEdit.emit(data);
  }

  confirmDialogUpdateTaskFlow(){
    this.confirmationService.confirm({
      message: this.translate.instant('messages.message_update') + " <b>" + this.selectedFlow?.name + "</b>?",
      header: this.translate.instant("flow.update_flow"),
      icon: 'pi pi-exclamation-triangle',
      acceptIcon:"none",
      rejectIcon:"none",
      rejectButtonStyleClass:"p-button-text",
      acceptButtonStyleClass:"ng-confirm-button",
      acceptLabel: this.translate.instant("save"),
      rejectLabel: this.translate.instant("no"),
      accept: () => {
          this.acceptSaveTaskFlow();
      },
      reject: () => {
        this.resetInactivityMonitor();
      }
    });

    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }

  updateTaskFlow(data: TaskFlowEntity){
    this.consoleLogger.debug(data);
    this.selectedFlow = data;
    this.getTaskFlowByIdUseCase.execute({id: data.id!}).then(
      (data) => {
        this.form.patchValue({
          flowName: data.name,
          flowDescription: data.description,
          isPublished: data.isPublished,
        });
      },
      (e) => {
        this.consoleLogger.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.RECORD_ID, value: data.id?.toString() }
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_TASK_FLOW_BY_ID, 0, 'ERROR', '', at_attributes);
      }
    )
    this.showUpdateTaskFlowDialog = true;
  }

  acceptSaveTaskFlow(){
    const taskFlow: TaskFlowEntity = {
      id: this.selectedFlow?.id,
      name: this.form.get('flowName')?.value,
      description: this.form.get('flowDescription')?.value,
      isPublished: this.form.get('isPublished')?.value,
      flowActions: this.selectedFlow?.flowActions
    }
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(this.selectedFlow) },
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(taskFlow) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.MOD_FLW, ReasonActionTypeEnum.UPDATE, () => {
      this.onSubmitTaksFlow(taskFlow);
     }, at_attributes);
  }

  onSubmitTaksFlow(taskFlow: TaskFlowEntity){
    this.updateTaskFlowUseCase.execute({taskFlow: taskFlow}).then(
      (data) => {
        this.consoleLogger.debug(data);
        this.showUpdateTaskFlowDialog = false;
        this.messageService.add({severity:'success', summary: this.translate.instant("titles.success_operation"), detail:this.translate.instant("messages.success_general"),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000});
      },
      (e) => {
        this.messageService.add({
          severity:'error',
          summary: this.translate.instant("titles.error_operation"),
          detail:this.translate.instant("messages.error_updating_task_flow"),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.consoleLogger.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.RECORD_ID, value: taskFlow.id?.toString() },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(this.selectedFlow) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(taskFlow) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_FLW, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  createNewFlow() {
    this.onNewDataFlow.emit(true);
  }

  flowStatus(status: boolean){
    if(status){
      return this.translate.instant('flow.published');
    }else{
      return this.translate.instant('flow.no_published');
    }
  }

  /* Search */
  onFilter(event: any, dt: Table) {
    if(!event.filters['updatedAt'].value){
      this.rangeDates = null;
      this.formGroup.reset();
    }
  }

  /* Date Range Filter */
  applyDateRangeFilter(dt: Table, field: string) {
    this.rangeDates = this.formGroup.get('date')?.value;
    dt.filter({
      startDate: this.rangeDates ? this.rangeDates[0] : null,
      endDate: this.rangeDates ? this.rangeDates[1] : null
    }, field, 'customDateRange');
  }

  closeConfirmationDialog() {
    this.confirmationService.close();
    this.resetInactivityMonitor();
  }
}
