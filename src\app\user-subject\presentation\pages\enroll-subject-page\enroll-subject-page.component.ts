import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewContainerRef } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { ConfirmationService, MenuItem, MessageService } from "primeng/api";
import { CheckTokenUseCase } from "src/verazial-common-frontend/core/general/auth/domain/use-cases/check-token.use-case";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { AccessIdentifier } from "src/verazial-common-frontend/core/models/access-identifier.enum";
import { UserSubjectActionType } from "src/verazial-common-frontend/core/models/user-subject-action-type.enum";
import { CheckPermissionsService } from "src/verazial-common-frontend/core/services/check-permissions-service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { DynamicFormAttributes, DynamicForm<PERSON><PERSON>po<PERSON> } from "../../components/dynamic-form/dynamic-form/dynamic-form.component";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { CustomFieldModel } from "src/verazial-common-frontend/core/general/manager/common/models/custom-field.model";
import { GetSettingsByApplicationUseCase } from "src/verazial-common-frontend/core/general/manager/domain/use-cases/get-settings-by-application.use-case";
import { environment } from "src/environments/environment";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { CustomFieldTypes } from "src/verazial-common-frontend/core/general/manager/common/models/custom-field-type.enum";
import { SubjectDetailEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject-detail.entity";
import { SaveSubjectDetailUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/save-subject-detail.use-case";
import { TranslateService } from "@ngx-translate/core";
import { GetAllRolesBySubjectIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-all-roles-by-subject-id.use-case";
import { RoleEntity } from "src/verazial-common-frontend/core/general/common/entity/role.entity";
import { SubjectRoleEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject-role.entity";
import { DeleteSubjectRoleByIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/delete-subject-role-by-id.use-case";
import { AddSubjectRolesUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/add-subject-roles.use-case";
import { UpdateSubjectUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/update-subject.use-case";
import { GetStaticResourcesBySubjectIdAndNameUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/get-static-resources-by-subject-id-and-name.use-case";
import { CreateStaticResourceEntity } from "src/verazial-common-frontend/core/general/storage/domain/entity/create-static-resource.entity";
import { CreateStaticResourceUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/create-static-resource.use-case";
import { SaveSubjectUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/save-subject.use-case";
import { GetSubjectByNumIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subject-by-num-id.use-case";
import { GetSubjectDetailsBySubjectIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subject-details-by-subject-id.use-case";
import { WidgetResult } from "src/verazial-common-frontend/modules/shared/models/widget-response.model";
import { TabViewChangeEvent } from "primeng/tabview";
import { SubjectTabsConfig } from "src/verazial-common-frontend/core/general/manager/common/models/subject-tabs-config.module";
import { GetKonektorPropertiesUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case";
import { PrisonsSettingsModel } from "src/verazial-common-frontend/core/general/manager/common/models/prisons-settings.model";
import { KonektorPropertiesEntity } from "src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity";

@Component({
    selector: 'app-enroll-subject-page',
    templateUrl: './enroll-subject-page.component.html',
    styleUrl: './enroll-subject-page.component.css',
    providers: [MessageService, ConfirmationService]
})
export class EnrollSubjectPageComponent implements OnInit, OnDestroy {

    /* Subject Details Form */
    @ViewChild('dynamicFormContent', { read: ViewContainerRef, static: false }) dynamicFormContent!: ViewContainerRef;
    formAttributes: DynamicFormAttributes[] = [];
    showDynamicForm: boolean = false;
    formInStepper: boolean = true;
    componentRef: any;
    /* Settings */
    managerSettings?: GeneralSettings;
    konektorProperties?: KonektorPropertiesEntity;
    /* Enroll Page */
    step?: string;
    steps: string[] = [];
    readAndWritePermissions: boolean = false;
    readOnly: boolean = false;
    isUser: boolean = false;
    actionType: UserSubjectActionType = UserSubjectActionType.CREATE;
    userSubjectActionTypes = UserSubjectActionType;
    /* Subject Data */
    nId?: string;
    subjectData: SubjectEntity | undefined = undefined;
    userSubjectDetails: SubjectDetailEntity[] = [];
    subjectDataEdit: SubjectEntity | undefined = undefined;
    modified: boolean = false;
    pictureModified: boolean = false;
    pictureUploaded: boolean = false;
    biometricSamples: boolean = false;
    /* Page State */
    isLoading: boolean = false;
    tech: string = '';
    isDisableSaveButton: boolean = true;
    saveAllowed: boolean = true;
    isDisableContinueButton: boolean = true;
    profilePicPlaceholder: string = 'verazial-common-frontend/assets/images/all/UserPic.svg';
    picHistoryName: string = 'profile-picture-history';
    subjectTabsConfig: SubjectTabsConfig = new SubjectTabsConfig();
    prisonsConfig?: PrisonsSettingsModel = new PrisonsSettingsModel();
    showExtendedBioFieldsTab: boolean = false;
    showPhysicalDataTab: boolean = false;
    showLocationsTab: boolean = false;
    showEntriesExitsTab: boolean = false;
    showJudicialFileTab: boolean = false;
    showBelongingsTab: boolean = false;
    /* New Subject Internal Stepper */
    items: MenuItem[] = [];
    activeIndex: number = 0;
    /* User in Session */
    userIsVerified: boolean = false;
    /* Widget */
    widgetUrl: string = "";
    widgetReady: boolean = false;
    updateAttributes: boolean = false;
    singleTechEnroll: boolean = false;

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private translateService: TranslateService,
        private localStorageService: LocalStorageService,
        private checkTokenUseCase: CheckTokenUseCase,
        private checkPermissions: CheckPermissionsService,
        private loggerService: ConsoleLoggerService,
        private getSettingsByApplicationUseCase: GetSettingsByApplicationUseCase,
        private auditTrailService: AuditTrailService,
        private saveSubjectDetailUseCase: SaveSubjectDetailUseCase,
        private messageService: MessageService,
        private getAllRolesBySubjectIdUseCase: GetAllRolesBySubjectIdUseCase,
        private deleteSubjectRoleByIdUseCase: DeleteSubjectRoleByIdUseCase,
        private addSubjectRolesUseCase: AddSubjectRolesUseCase,
        private updateSubjectUseCase: UpdateSubjectUseCase,
        private getStaticResourcesBySubjectIdAndNameUseCase: GetStaticResourcesBySubjectIdAndNameUseCase,
        private createStaticResourceUseCase: CreateStaticResourceUseCase,
        private saveSubjectUseCase: SaveSubjectUseCase,
        private getSubjectByNumIdUseCase: GetSubjectByNumIdUseCase,
        private getSubjectDetailsBySubjectIdUseCase: GetSubjectDetailsBySubjectIdUseCase,
        private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
    ) {
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    }

    /* Component Functions */

    ngOnInit(): void {
        this.isLoading = true;
        this.getManagerSettings();
    }

    ngOnDestroy(): void {
        this.localStorageService.setItem('enrolled', '');
        this.updateModified(false);
    }

    handleBeforeUnload(event: Event) {
        this.ngOnDestroy();
    }

    /* Initialization */

    async initEnroll() {
        await this.checkUserVerified();
        this.readAndWritePermissions = this.checkPermissions.hasReadAndWritePermissions(AccessIdentifier.SUBJECT);
        if (!this.readAndWritePermissions) {
            this.readOnly = this.checkPermissions.hasReadPermissions(AccessIdentifier.SUBJECT);
        }
        if (!this.readAndWritePermissions && !this.readOnly) {
            this.router.navigate(['/error/403']);
        }
        this.activeIndex = 0;
        this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
                this.konektorProperties = data;
                if (data.apiGatewayGrpc) {
                    this.localStorageService.setApiGatewayURL(data.apiGatewayGrpc);
                }
                else {
                    this.localStorageService.destroyApiGatewayURL();
                }
                this.buildSteps()
                if (!this.step) {
                    this.route.params.subscribe(params => {
                        this.isLoading = true;
                        if (this.nId && this.step == 'new') {
                            location.reload();
                        }
                        this.checkUserVerified();
                        this.widgetReady = false;
                        this.step = params['step'];
                        this.buildStepper();
                        if (this.step != 'new' && this.step != 'continued' && this.step != 'summary') {
                            this.tech = this.step!;
                            switch (this.tech) {
                                case 'facial':
                                    if (this.managerSettings?.payedTechnology?.facial != true || data.enabledTech?.facial != true) {
                                        this.router.navigate(['/general/enroll/new']);
                                    }
                                    break;
                                case 'iris':
                                    if (this.managerSettings?.payedTechnology?.iris != true || data.enabledTech?.iris != true) {
                                        this.router.navigate(['/general/enroll/new']);
                                    }
                                    break;
                                case 'fingerprint':
                                    if (this.managerSettings?.payedTechnology?.dactilar != true || data.enabledTech?.dactilar != true) {
                                        this.router.navigate(['/general/enroll/new']);
                                    }
                                    break;
                                case 'fingerprintRolled':
                                    if (this.managerSettings?.payedTechnology?.dactilarRolled != true || data.enabledTech?.dactilarRolled != true) {
                                        this.router.navigate(['/general/enroll/new']);
                                    }
                                    break;
                                case 'palm':
                                    if (this.managerSettings?.payedTechnology?.palm != true || data.enabledTech?.palm != true) {
                                        this.router.navigate(['/general/enroll/new']);
                                    }
                                    break;
                            }
                            this.singleTechEnroll = true;
                            if (this.componentRef) this.componentRef.destroy();
                        }
                        else {
                            this.tech = '';
                            this.singleTechEnroll = false;
                            if (this.step == 'summary') {
                                this.formInStepper = false;
                            }
                            if (this.step == 'continued') {
                                this.formInStepper = false;
                                if (this.items.length <= 0) {
                                    this.onSubmitContinue();
                                }
                            }
                            if (this.step == 'new') {
                                this.formInStepper = true;
                            }
                        }
                        this.route.queryParams.subscribe(params => {
                            if (this.step != 'new') {
                                if (params['nId']) {
                                    this.nId = params['nId'];
                                }
                                if (this.nId) {
                                    this.getSubjectByNumId(this.nId);
                                }
                                else {
                                    this.router.navigate(['/general/enroll/new']);
                                }
                            }
                            else {
                                if (this.nId && this.step == 'new') {
                                    location.reload();
                                }
                            }
                        });
                        this.isLoading = false;
                    });
                    this.isLoading = false;
                }
                else {
                    this.isLoading = true;
                    this.router.navigate(['/general/enroll/new']);
                }
            },
            error: (e) => {
                this.isLoading = false;
                this.loggerService.error('Error Getting Konektor Properties:');
                this.loggerService.error(e);
            }
        });
    }

    buildSteps() {
        this.steps.push('new');
        if (this.managerSettings?.payedTechnology?.iris == true && this.konektorProperties?.enabledTech?.iris == true) {
            this.steps.push('iris');
        }
        if (this.managerSettings?.payedTechnology?.dactilar == true && this.konektorProperties?.enabledTech?.dactilar == true) {
            this.steps.push('fingerprint');
        }
        if (this.managerSettings?.payedTechnology?.facial == true && this.konektorProperties?.enabledTech?.facial == true) {
            this.steps.push('facial');
        }
        if (this.managerSettings?.payedTechnology?.dactilarRolled == true && this.konektorProperties?.enabledTech?.dactilarRolled == true) {
            this.steps.push('fingerprintRolled');
        }
        if (this.managerSettings?.payedTechnology?.palm == true && this.konektorProperties?.enabledTech?.palm == true) {
            this.steps.push('palm');
        }
        this.steps.push('continued')
        this.steps.push('summary');
    }

    buildStepper() {
        this.items = [];
        if (this.step == 'new') {
            this.items.push({ label: this.translateService.instant('titles.biographic_data'), })
            this.showExtendedBioFieldsTab = this.subjectTabsConfig.showExtendedBioFieldsTab && this.isTabEnabled(this.subjectTabsConfig?.restrictExtendedBioFieldsTabToSpecificRoles!, this.subjectTabsConfig?.extendedBioFieldsRoles!) || false;
            if (this.showExtendedBioFieldsTab) {
                this.items.push({ label: this.translateService.instant('titles.extended_biographic_data'), });
            }
            this.showJudicialFileTab = (this.prisonsConfig?.isPrisonsEnabled! && this.prisonsConfig!.showJudicialFileTab! && this.isPrisoner()) || false;
            if (this.showJudicialFileTab) {
                this.items.push({ label: this.translateService.instant('headers.judicial_files'), });
            }
            this.showPhysicalDataTab = this.subjectTabsConfig.showPhysicalDataTab && this.isTabEnabled(this.subjectTabsConfig?.restrictPhysicalDataTabToSpecificRoles!, this.subjectTabsConfig?.physicalDataRoles!) || false;
            if (this.showPhysicalDataTab) {
                this.items.push({ label: this.translateService.instant('titles.physical_data'), });
            }
        }
        else if (this.step == 'continued') {
            this.showLocationsTab = this.subjectTabsConfig.showLocationsTab && this.isTabEnabled(this.subjectTabsConfig?.restrictLocationsTabToSpecificRoles!, this.subjectTabsConfig?.locationsRoles!) || false;
            if (this.showLocationsTab) {
                this.items.push({ label: this.translateService.instant('headers.locations'), });
            }
            this.showEntriesExitsTab = this.subjectTabsConfig.showEntriesExitsTab && this.isTabEnabled(this.subjectTabsConfig?.restrictEntriesExitsTabToSpecificRoles!, this.subjectTabsConfig?.entriesExitsRoles!) || false;
            if (this.showEntriesExitsTab) {
                this.items.push({ label: this.translateService.instant('titles.entries_and_exits'), });
            }
            this.showBelongingsTab = (this.prisonsConfig?.isPrisonsEnabled! && this.prisonsConfig!.showBelongingsTab! && this.isPrisoner()) || false;
            if (this.showBelongingsTab) {
                this.items.push({ label: this.translateService.instant('headers.belongings'), });
            }
        }
    }

    getSubjectByNumId(numId: string) {
        this.isLoading = true;
        this.getSubjectByNumIdUseCase.execute({ numId: numId }).then(
            (data) => {
                if (data) {
                    this.loggerService.debug('Subject Data Retrieved Successfully:');
                    this.loggerService.debug(data);
                    this.localStorageService.setItem('enrolled', numId);
                    this.actionType = UserSubjectActionType.UPDATE;
                    this.subjectData = { ...data };
                    this.subjectDataEdit = { ...data };
                    this.isDisableContinueButton = false;
                    this.widgetReady = true;
                    this.getSubjectDetails();
                    this.activeIndex = 0;
                    this.buildStepper();
                }
            },
            (e) => {
                this.loggerService.error('Error Retrieving Subject Data:');
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                    { name: AuditTrailFields.SUBJECT_NUM_ID, value: numId }
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SUBJECT_BY_NUM_ID, 0, 'ERROR', '', at_attributes);
                // this.navigateToErrorPage('timesError', 'dataInconsistencyErrorTitle', 'dataInconsistencyErrorDescription');
                this.router.navigate(['/general/enroll/new']);
            },
        )
        .finally(() => {
            this.isLoading = false;
        });
    }

    /* Logged User Biometric Verification */

    async checkUserVerified() {
        if (this.localStorageService.isUserVerified()) {
            await this.checkTokenUseCase.execute({ token: this.localStorageService.getItem('bio_auth')! }).then(
                (response) => {
                    this.userIsVerified = !response;
                },
                (e) => {
                    this.userIsVerified = false;
                }
            );
        }
        else {
            this.userIsVerified = false;
        }
    }

    async userVerifying(mod: boolean) {
        this.widgetReady = false;
        this.updateModified(mod);
        await this.checkUserVerified();
        if (!mod) {
            this.widgetReady = true;
        }
    }

    /* Manager Functions */
    getManagerSettings() {
        this.getSettingsByApplicationUseCase.execute({ applicationName: environment.application }).then(
            (data) => {
                if (data) {
                    this.loggerService.debug('Settings Data Retrieved Successfully:');
                    this.loggerService.debug(data);
                    this.managerSettings = data.settings;
                    if (this.managerSettings) {
                        this.subjectTabsConfig = this.managerSettings.subjectTabsConfig || new SubjectTabsConfig();
                        this.prisonsConfig = this.managerSettings.continued1?.prisonsSettings || new PrisonsSettingsModel();
                        this.widgetUrl = this.managerSettings.widgetConfig?.url || "";
                        this.localStorageService.setSessionSettings(this.managerSettings);
                        this.initEnroll();
                    }
                    else {
                        this.isLoading = false;
                        this.loggerService.error('No Settings Data Retrieved');
                        this.loggerService.error(data);
                    }
                }
            },
            (e) => {
                this.isLoading = false;
                this.loggerService.error('Error Retrieving Settings Data:');
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                    { name: AuditTrailFields.APPLICATION_ID, value: environment.application }
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SETTINGS_BY_APPLICATION, 0, 'ERROR', '', at_attributes);
            },
        );
    }

    /* Biographic Data Functions */

    onSubjectChanged(data: SubjectEntity) {
        // this.loggerService.debug('Subject Data Changed:');
        // this.loggerService.debug(data);
        this.subjectDataEdit = data;
        if (data.numId && data.names && data.lastNames && data.roles?.length! > 0 && data.defaultRole) {
            this.isDisableSaveButton = false;
        }
        else {
            this.isDisableSaveButton = true;
        }
    }

    onSubmitSubjectPicture(event: {pic: string, uploaded: boolean}) {
        if (this.subjectData?.pic != event.pic) {
            // this.updateModified(true);
            this.pictureModified = true;
            this.pictureUploaded = event.uploaded;
            this.onSubjectChanged(this.subjectDataEdit!);
        }
    }

    allowSave(allow: boolean) {
        this.saveAllowed = allow;
    }

    onSubmitSubject() {
        if (this.subjectData != undefined) {
            const at_attributes: ExtraData[] = [
                { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.subjectDataEdit) },
                { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(this.subjectData) }
            ];
            this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.SUBJECT, AuditTrailActions.MOD_SUB, ReasonActionTypeEnum.UPDATE, () => { this.updateSubject(); }, at_attributes);
        }
        else {
            const at_attributes: ExtraData[] = [
                { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.subjectDataEdit) },
                { name: AuditTrailFields.SUBJECT_NUM_ID, value: this.subjectDataEdit?.numId },
                { name: AuditTrailFields.SUBJECT_ROLES, value: JSON.stringify(this.subjectDataEdit?.roles) },
            ];
            this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.SUBJECT, AuditTrailActions.ADD_SUB, ReasonActionTypeEnum.CREATE, () => { this.addNewSubject(this.subjectDataEdit!); }, at_attributes);
        }
    }

    updateSubject() {
        this.isLoading = true;
        this.subjectDataEdit!.showPic = this.subjectDataEdit?.pic != null && this.subjectDataEdit?.pic != undefined && this.subjectDataEdit?.pic != "" && this.subjectDataEdit?.pic != this.profilePicPlaceholder;
        const userSubject = this.subjectDataEdit;
        if (userSubject) {
            this.updateUserSubjectRoles(userSubject);
            this.updateSubjectUseCase.execute({ subject: userSubject }).then(
                (response) => {
                    if (response.success) {
                        this.subjectData = { ...this.subjectDataEdit! };
                        if (this.step == 'new') {
                            this.updateNewInternalStep(this.activeIndex + 1);
                        }
                        this.messageService.add({
                            severity: 'success',
                            summary: this.translateService.instant('content.successTitle'),
                            detail: this.translateService.instant('messages.success_general'),
                            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                        });
                        if (this.pictureModified) {
                            this.pictureModified = false;
                            this.updateUserSubjectPicHistory(this.subjectData?.pic!);
                        }
                        this.updateModified(false);
                    }
                    else {
                        this.messageService.add({
                            severity: 'error',
                            summary: this.translateService.instant('content.errorTitle'),
                            detail: `${this.translateService.instant('messages.error_updating_subject')}: ${response}`,
                            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                        });
                        this.loggerService.error('Error Updating Subject Data:');
                        this.loggerService.error(response);
                        const at_attributes: ExtraData[] = [
                            { name: AuditTrailFields.ERROR, value: JSON.stringify(response) },
                            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.subjectData) },
                            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(userSubject) }
                        ];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_SUB, 0, 'ERROR', '', at_attributes);
                    }
                },
                (e) => {
                    this.loggerService.error('Error Updating Subject Data:');
                    this.loggerService.error(e);
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.subjectData) },
                        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(userSubject) }
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_SUB, 0, 'ERROR', '', at_attributes);
                    this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('content.errorTitle'),
                        detail: `${this.translateService.instant('messages.error_updating_subject')}: ${e.message}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                }
            )
            .finally(() => {
                this.isLoading = false;
                this.updateAttributes = true;
                this.widgetReady = false;
                if (!this.widgetReady) {
                    setTimeout(() => this.widgetReady = true, 0);
                }
            });
        }
        this.isLoading = false;
    }

    addNewSubject(subject: SubjectEntity) {
        subject!.showPic = subject?.pic != null && subject?.pic != undefined && subject?.pic != "" && subject?.pic != this.profilePicPlaceholder;
        this.saveSubjectUseCase.execute({ subject: subject }).then(
            (data) => {
                this.localStorageService.setItem('enrolled', data?.numId!);
                this.subjectData = { ...data };
                this.subjectDataEdit = { ...data };
                this.actionType = UserSubjectActionType.UPDATE;
                this.isDisableContinueButton = false;
                if (this.pictureModified) {
                    this.pictureModified = false;
                    this.updateUserSubjectPicHistory(subject.pic!);
                }
                this.messageService.add({
                    severity: 'success',
                    summary: this.translateService.instant('content.successTitle'),
                    detail: this.translateService.instant('messages.success_general'),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.buildStepper();
                if (
                    this.showPhysicalDataTab || this.showExtendedBioFieldsTab
                ) {if (this.showExtendedBioFieldsTab) {
                        this.showDynamicForm = true;
                        this.getSubjectDetails();
                    }
                }
                this.updateNewInternalStep(this.activeIndex + 1);
            },
            (e) => {
                let errorMessage = 'messages.error_creating_subject';
                if (e.code == 2000) errorMessage = 'messages.duplicate_subject_numId';
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('content.errorTitle'),
                    detail: `${this.translateService.instant(errorMessage)}: ${e.message}`,
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                    { name: AuditTrailFields.ERROR_MESSAGE, value: this.translateService.instant(errorMessage) },
                    { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(subject) },
                    { name: AuditTrailFields.SUBJECT_NUM_ID, value: subject.numId },
                    { name: AuditTrailFields.SUBJECT_ROLES, value: JSON.stringify(subject.roles) },
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_SUB, 0, 'ERROR', '', at_attributes);
            }
        )
            .finally(() => {
                this.updateAttributes = true;
                this.widgetReady = false;
                if (!this.widgetReady) {
                    setTimeout(() => this.widgetReady = true, 0);
                }
            });
    }

    updateUserSubjectRoles(userSubject: SubjectEntity) {
        let newRoleIds: number[] = [];
        userSubject.roles?.forEach((role: RoleEntity) => {
            newRoleIds.push(role.id!);
        });
        if (newRoleIds.length > 0) {
            this.getAllRolesBySubjectIdUseCase.execute({ subjectId: userSubject.id! }).then(
                (data: SubjectRoleEntity[]) => {
                    if (data.length > 0) {
                        let currentRoleIds: number[] = [];
                        data.forEach((role: SubjectRoleEntity) => {
                            currentRoleIds.push(role.roleId!);
                        });

                        let same = (currentRoleIds.length !== newRoleIds.length);

                        // Sort arrays and compare
                        const sortedArr1 = [...currentRoleIds].sort((a, b) => a - b);
                        const sortedArr2 = [...newRoleIds].sort((a, b) => a - b);

                        same = sortedArr1.every((value, index) => value === sortedArr2[index]);
                        if (!same) {
                            const removed = currentRoleIds.filter(value => !newRoleIds.includes(value));
                            if (removed.length > 0) {
                                let rolesToRemove: SubjectRoleEntity[] = [];
                                data.forEach((role: SubjectRoleEntity) => {
                                    if (removed.includes(role.roleId!)) {
                                        rolesToRemove.push(role);
                                    }
                                });
                                this.deleteSubjectRoleByIdUseCase.execute({ listSubjectRoles: rolesToRemove }).then(
                                    (response) => {
                                        if (response) {
                                            this.loggerService.debug('Profiles Deleted Successfully:');
                                            this.loggerService.debug(response);
                                            const at_attributes: ExtraData[] = [
                                                { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(rolesToRemove) },
                                            ];
                                            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DELETE_SUBJECT_ROLES, 0, 'SUCCESS', '', at_attributes);
                                        }
                                    },
                                    (e) => {
                                        this.loggerService.error('Error Deleting Profiles:');
                                        this.loggerService.error(e);
                                        const at_attributes: ExtraData[] = [
                                            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                                            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(rolesToRemove) },
                                        ];
                                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DELETE_SUBJECT_ROLES, 0, 'ERROR', '', at_attributes);
                                        this.messageService.add({
                                            severity: 'error',
                                            summary: this.translateService.instant('content.errorTitle'),
                                            detail: `${this.translateService.instant('messages.error_deleting_profiles')}: ${e.message}`,
                                            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                                        });
                                    }
                                );
                            }
                            const added = newRoleIds.filter(value => !currentRoleIds.includes(value));
                            if (added.length > 0) {
                                let rolesToAdd: SubjectRoleEntity[] = [];
                                added.forEach((role: number) => {
                                    rolesToAdd.push({ subjectId: userSubject.id!, roleId: role } as SubjectRoleEntity);
                                });
                                this.addSubjectRolesUseCase.execute({ listSubjectRoles: rolesToAdd }).then(
                                    (response) => {
                                        if (response) {
                                            this.loggerService.debug('Profiles Added Successfully:');
                                            this.loggerService.debug(response);
                                            const at_attributes: ExtraData[] = [
                                                { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(rolesToAdd) },
                                            ];
                                            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_SUBJECT_ROLES, 0, 'SUCCESS', '', at_attributes);
                                        }
                                    },
                                    (e) => {
                                        this.loggerService.error('Error Adding Profiles:');
                                        this.loggerService.error(e);
                                        const at_attributes: ExtraData[] = [
                                            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                                            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(rolesToAdd) },
                                        ];
                                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_SUBJECT_ROLES, 0, 'ERROR', '', at_attributes);
                                        this.messageService.add({
                                            severity: 'error',
                                            summary: this.translateService.instant('content.errorTitle'),
                                            detail: `${this.translateService.instant('messages.error_adding_profiles')}: ${e.message}`,
                                            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                                        });
                                    }
                                );
                            }
                        }
                    }
                    else {
                        let rolesToAdd: SubjectRoleEntity[] = [];
                        newRoleIds.forEach((role: number) => {
                            rolesToAdd.push({ subjectId: userSubject.id!, roleId: role } as SubjectRoleEntity);
                        });
                        this.addSubjectRolesUseCase.execute({ listSubjectRoles: rolesToAdd }).then(
                            (response) => {
                                if (response) {
                                    this.loggerService.debug('Profiles Added Successfully:');
                                    this.loggerService.debug(response);
                                    const at_attributes: ExtraData[] = [
                                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(rolesToAdd) },
                                    ];
                                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_SUBJECT_ROLES, 0, 'SUCCESS', '', at_attributes);
                                }
                            },
                            (e) => {
                                this.loggerService.error('Error Adding Profiles:');
                                this.loggerService.error(e);
                                const at_attributes: ExtraData[] = [
                                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                                    { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(rolesToAdd) },
                                ];
                                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_SUBJECT_ROLES, 0, 'ERROR', '', at_attributes);
                                this.messageService.add({
                                    severity: 'error',
                                    summary: this.translateService.instant('content.errorTitle'),
                                    detail: `${this.translateService.instant('messages.error_adding_profiles')}: ${e.message}`,
                                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                                });
                            }
                        );
                    }
                },
                (e) => {
                    this.loggerService.error('Error Retrieving Profiles:');
                    this.loggerService.error(e);
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.SUBJECT_ID, value: userSubject.id }
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_ROLES_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
                    this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('content.errorTitle'),
                        detail: `${this.translateService.instant('messages.error_retrieving_profiles')}: ${e.message}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                }
            );
        }
    }

    updateUserSubjectPicHistory(pic: string) {
        this.isLoading = true;
        if (pic != null && pic != undefined && pic != "" && pic != this.profilePicPlaceholder) {
            this.getStaticResourcesBySubjectIdAndNameUseCase.execute({ subjectId: this.subjectData?.id!, name: this.picHistoryName }).then(
                (data) => {
                    if (data) {
                        const number = data.length;
                        let newStaticResource = new CreateStaticResourceEntity();
                        // subjectId?: string;
                        newStaticResource.subjectId = this.subjectData?.id!;
                        // name?: string;
                        newStaticResource.name = this.picHistoryName;
                        // number?: number;
                        newStaticResource.number = number;
                        // content?: string;
                        newStaticResource.content = pic.replace('data:image/jpeg;base64,', '');
                        // description?: string;
                        newStaticResource.description = this.pictureUploaded ? 'uploaded' : undefined;
                        // async?: boolean;
                        newStaticResource.async = false;
                        this.createStaticResourceUseCase.execute({ createStaticResourceRequest: newStaticResource }).then(
                            () => {
                                const at_attributes: ExtraData[] = [
                                    { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(newStaticResource) },
                                    { name: AuditTrailFields.RECORD_NAME, value: newStaticResource.name! },
                                    { name: AuditTrailFields.RECORD_NUMBER, value: newStaticResource.number!.toString() }
                                ];
                                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_STA_RES, 0, 'SUCCESS', '', at_attributes);
                                this.messageService.add({
                                    severity: 'success',
                                    summary: this.translateService.instant('content.successTitle'),
                                    detail: this.translateService.instant('messages.success_general'),
                                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                                });
                                this.subjectData = { ... this.subjectData! };
                            },
                            (e) => {
                                this.loggerService.error('Error Creating User/Subject Static Resource:');
                                this.loggerService.error(e);
                                const at_attributes: ExtraData[] = [
                                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                                    { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(newStaticResource) },
                                    { name: AuditTrailFields.RECORD_NAME, value: newStaticResource.name! },
                                    { name: AuditTrailFields.RECORD_NUMBER, value: newStaticResource.number!.toString() }
                                ];
                                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_STA_RES, 0, 'ERROR', '', at_attributes);
                                this.messageService.add({
                                    severity: 'error',
                                    summary: this.translateService.instant('content.errorTitle'),
                                    detail: `${this.translateService.instant('messages.error_uploading_image')}: ${e.message}`,
                                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                                });
                            }
                        );
                    }
                },
                (e) => {
                    this.loggerService.error('Error Getting User/Subject Static Resources:');
                    this.loggerService.error(e);
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.SUBJECT_ID, value: this.subjectData?.id! },
                        { name: AuditTrailFields.RECORD_NAME, value: this.picHistoryName }
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_STATIC_RESOURCE_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
                    this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('content.errorTitle'),
                        detail: `${this.translateService.instant('messages.error_downloading_image')}: ${e.message}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                }
            )
            .finally(() => {
                this.isLoading = false;
                this.pictureUploaded = false;
            });
        }
        else {
            this.isLoading = false;
            this.pictureUploaded = false;
        }
    }

    /* Dynamic Form Functions */

    getSubjectDetails() {
        this.getSubjectDetailsBySubjectIdUseCase.execute({ id: this.subjectData?.id! }).then(
            (data) => {
                if (data) {
                    this.loggerService.debug('Subject Details Data Retrieved Successfully:');
                    this.loggerService.debug(data);
                    this.userSubjectDetails = data;
                }
                else {
                    this.loggerService.error('No Subject Details Data Retrieved');
                }
            },
            (e) => {
                this.loggerService.error('Error Retrieving Subject Details Data:');
                this.loggerService.error(e);
                if (e.code != 404) {
                    const at_attributes: ExtraData[] = [{ name: 'error', value: JSON.stringify(e) }];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SUBJECT_DETAILS_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
                    this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('content.errorTitle'),
                        detail: `${this.translateService.instant('messages.error_obtaining_extended_biographic_data')}: ${e.message}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                }
                else {
                    this.userSubjectDetails = [];
                }
            },
        )
        .finally(() => {
            if(this.tech == '') {
                this.buildExtendedBioForm();
            }
        });
    }

    buildExtendedBioForm() {
        const extBioFields: CustomFieldModel[] = this.managerSettings?.extBioFields || [];
        if (extBioFields.length != 0) {
            this.loggerService.info('There are Extended Bio Fields to submit');
            this.formAttributes = [];
            // Classify the attributes of the action
            extBioFields.forEach((extBioField: CustomFieldModel) => {
                if (extBioField.type == CustomFieldTypes.INPUT || extBioField.type == CustomFieldTypes.DROPDOWN || extBioField.type == CustomFieldTypes.TOGGLE) {
                    let type: CustomFieldTypes = extBioField.type;
                    let required = false;
                    let maxCharacters = 0;
                    let options: string[] = [];
                    extBioField.fieldData?.forEach((data: any) => {
                        if (data.key.includes('required-checkbox')) {
                            required = data.value == 'true';
                        }
                        if (data.key.includes('options-listbox')) {
                            options = JSON.parse(data.value);
                        }
                        if (data.key.includes('max-characters-textbox')) {
                            maxCharacters = data.value;
                        }
                    });
                    if (type == CustomFieldTypes.INPUT && maxCharacters > (this.managerSettings?.continued1?.inputTextAreaThreshold ?? 25)) {
                        type = CustomFieldTypes.TEXTAREA;
                    }
                    const formAttribute: DynamicFormAttributes = {
                        type: type,
                        label: extBioField.name,
                        key: extBioField.parameter,
                        value: this.userSubjectDetails.find((detail: SubjectDetailEntity) => detail.parameter == extBioField.parameter)?.value || '',
                        detailId: this.userSubjectDetails.find((detail: SubjectDetailEntity) => detail.parameter == extBioField.parameter)?.id || '',
                        required: required,
                        options: options,
                        disabled: !this.readAndWritePermissions,
                        group: extBioField.group?.name ?? '',
                        groupRole: extBioField.group?.roleId ?? '',
                        tooltip: extBioField.description ?? '',
                    };
                    this.formAttributes.push(formAttribute);
                }
            });
            this.loggerService.debug(this.formAttributes);
            // If the action requires to show a form, render the dynamic form
            if (this.formAttributes.length > 0) {
                this.loggerService.info('There is a Form to Render');
                const formFields = this.convertFormControlArrayToObject(this.formAttributes);
                this.loggerService.debug(formFields);
                this.componentRef = this.dynamicFormContent.createComponent(DynamicFormComponent);
                this.componentRef.instance.controlsConfig = formFields;
                // this.componentRef.instance.formInStepper = this.formInStepper;
                this.componentRef.instance.showForm = this.showDynamicForm;
                this.componentRef.instance.canReadAndWrite = this.readAndWritePermissions;
                this.componentRef.instance.userSubjectRoles = this.subjectData?.roles;

                // Subscribe to the form submission event
                this.componentRef.instance.formSubmitted.subscribe((formData: any) => {
                    this.loggerService.debug('Form Data:');
                    this.loggerService.debug(formData);
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(formData) }
                    ];
                    this.auditTrailService.auditTrailSelectReason(this.isUser ? ReasonTypeEnum.USER : ReasonTypeEnum.SUBJECT, this.isUser ? AuditTrailActions.UPDATE_USER_DETAILS : AuditTrailActions.UPDATE_SUBJECT_DETAILS, ReasonActionTypeEnum.UPDATE, () => {
                        this.updateUserSubjectDetails(formData);
                    }, at_attributes);
                });
                // Subscribe to the form cancel event
                this.componentRef.instance.formCancel.subscribe((cancel: boolean) => {
                    this.updateModified(false);
                    if (this.step == 'new') {
                        this.updateNewInternalStep(this.activeIndex -1);
                    }
                });
                // Subscribe to the form modified event
                this.componentRef.instance.formModified.subscribe((modified: boolean) => {
                    this.updateModified(modified);
                });
            }
        }
    }

    updateUserSubjectDetails(formData: any) {
        this.loggerService.debug("Updating User/Subject Details");
        Object.keys(formData).forEach((key: string) => {
            const dynamicFormField = this.formAttributes.find((attr: DynamicFormAttributes) => attr.key == key);
            const foundDetail = this.userSubjectDetails.find((detail: SubjectDetailEntity) => detail.id == dynamicFormField?.detailId);
            let detail: SubjectDetailEntity = new SubjectDetailEntity();
            //id
            detail.id = foundDetail ? foundDetail.id : undefined;
            //tSubjectId
            detail.tSubjectId = this.subjectData?.id!;
            //name
            detail.name = dynamicFormField?.label;
            //parameter
            detail.parameter = key;
            //value
            detail.value = formData[key].toString();
            this.saveSubjectDetailUseCase.execute({ subjectDetail: detail }).then(
                (data) => {
                    if (data) {
                        this.loggerService.debug('Subject Detail Saved Successfully:');
                        this.loggerService.debug(data);
                    }
                },
                (e) => {
                    this.loggerService.error('Error Saving Subject Detail:');
                    this.loggerService.error(e);
                    this.loggerService.debug(detail);
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(detail) },
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.SAVE_SUBJECT_DETAIL, 0, 'ERROR', '', at_attributes);
                    this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('content.errorTitle'),
                        detail: `${this.translateService.instant('messages.error_saving_extended_biographic_data')}: ${e.message}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                }
            );
        });
        this.messageService.add({
            severity: 'success',
            summary: this.translateService.instant('content.successTitle'),
            detail: this.translateService.instant('messages.success_general'),
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
    }

    convertFormControlArrayToObject(arr: any[]) {
        const result: any = {};
        arr.forEach(item => {
            result[item.key] = item;
        });
        return result;
    }

    /* Widget Functions */

    onWidgetEnrollResult(event: WidgetResult) {
        if (!this.widgetReady) {
            return;
        }
        switch (event.action) {
            case "server_biodata":
                if (event.result == "success") {
                    this.biometricSamples = true;
                }
                break;
            case "server_update_attributes":
                if (event.result == "SUCCESS") {
                    this.updateAttributes = false;
                }
                break;
            case "process":
                this.updateModified(event.result.toLowerCase().includes("started"))
                break;
            case "enroll":
                this.biometricSamples = true;
                this.updateAttributes = false;
                /* A Subject gets created when a new user is created */
                // if (this.userSubjectData == undefined) {
                //   let userSubject = new UserEntity();
                //   userSubject.names = this.userSubject?.names;
                //   userSubject.lastNames = this.userSubject?.lastNames;
                //   userSubject.gender = this.userSubject?.gender;
                //   userSubject.pic = this.userSubject?.pic;
                //   userSubject.username = this.userSubject?.username;
                //   userSubject.email = this.userSubject?.email;
                //   userSubject.numId = this.userSubject?.numId;
                //   userSubject.birthdate = this.userSubject?.birthdate;
                //   this.onSubmitNewSubject(userSubject);
                // }
                break;
            case "close_verify":
            case "error":
                if (event.result == "NO_BIOMETRIC_IDENTITY") {
                    this.biometricSamples = false;
                }
                if (event.result == "ERROR_IDENTITY_DOES_NOT_EXIST") {
                    this.biometricSamples = false;
                }
                this.updateModified(false);
        }
    }

    /* Navigation */

    navigateHome() {
        this.updateModified(false);
        this.router.navigate(['/home']);
    }

    onSubmitContinue() {
        let adjacentSteps = this.getAdjacentSteps(this.step!);
        if (adjacentSteps.next) {
            // if (adjacentSteps.next == 'summary') {
            //     this.router.navigate(['general/subject/edit', 'subject', this.nId], { state: { verified: true } });
            // }
            // else {
                this.router.navigate(['/general/enroll/' + adjacentSteps.next], { queryParams: { nId: this.subjectData?.numId } });
            // }
        }
    }

    onSubmitBack() {
        let adjacentSteps = this.getAdjacentSteps(this.step!);
        if (adjacentSteps.previous) {
            this.router.navigate(['/general/enroll/' + adjacentSteps.previous], { queryParams: { nId: this.subjectData?.numId } });
        }
    }

    /* Support Functions */

  isPrisoner(): boolean {
    return this.subjectData! && this.subjectData?.roles?.filter((role: RoleEntity) => role.id?.toString() == this.prisonsConfig?.prisonerProfileId).length != 0;
  }

    updateModified(modified: boolean) {
        this.modified = modified;
        this.localStorageService.setLockMenu(modified);
    }

    updateNewInternalStep(step: number) {
        if (this.step == 'new') {
            this.showDynamicForm = this.showExtendedBioFieldsTab && step == 1;
            if (this.componentRef) this.componentRef.instance.showForm = this.showDynamicForm;
        }
        this.activeIndex = step;
        this.loggerService.debug(this.activeIndex);
        this.loggerService.debug(this.items.length);
        this.loggerService.debug(this.activeIndex == this.items.length);
        if (this.activeIndex == -1) {
            this.onSubmitBack();
        }
        if (this.activeIndex == this.items.length) {
            this.onSubmitContinue();
        }
    }

    onTabChange(event: TabViewChangeEvent) {
        this.showDynamicForm = event.index == 2;
        if (this.componentRef) this.componentRef.instance.showForm = this.showDynamicForm;
    }

    getAdjacentSteps(step: string): { previous: string | undefined, next: string | undefined } {
        const currentIndex = this.steps.indexOf(step);
        // If the step is not found, return undefined for both
        if (currentIndex === -1) {
            return { previous: undefined, next: undefined };
        }
        const previous = currentIndex > 0 ? this.steps[currentIndex - 1] : undefined;
        const next = currentIndex < this.steps.length - 1 ? this.steps[currentIndex + 1] : undefined;
        return { previous, next };
    }

    isTabEnabled(restricted: boolean, permittedRoles: string) {
        if (restricted && permittedRoles) {
            let isRoleIncluded = false;
            let roleIds = permittedRoles.split(',');
            let userSubject = this.subjectData;
            userSubject?.roles?.forEach((role: RoleEntity) => {
                if (roleIds.includes(role.id?.toString()!)) {
                    isRoleIncluded = true;
                }
            });
            return isRoleIncluded;
        }
        return true;
    }


    updateSegmentedAttributes(){
        //console.log("updateSegmentedAttributes: " + this.updateAttributes);
        this.updateAttributes = true;
    }
}