import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';

@Component({
  selector: 'app-example-audit-trail-with-inactivity',
  template: `
    <!-- Toast and Confirmation Dialog -->
    <p-toast></p-toast>
    <p-confirmDialog [style]="{width: '395px'}"></p-confirmDialog>
    
    <!-- Inactivity Monitor Component -->
    <app-inactivity-monitor
        [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
        [startChecking]="auditTrailService.startCheckingInactivity"
        (expired)="auditTrailService.closeConfirmationDialog()"
    ></app-inactivity-monitor>

    <!-- Your component content -->
    <div class="example-component">
      <h2>Example Component with Audit Trail and Inactivity Monitor</h2>
      
      <button 
        pButton 
        type="button" 
        label="Delete Item" 
        class="p-button-danger"
        (click)="deleteItem()">
      </button>
      
      <button 
        pButton 
        type="button" 
        label="Update Item" 
        class="p-button-warning"
        (click)="updateItem()">
      </button>
      
      <button 
        pButton 
        type="button" 
        label="Create Item" 
        class="p-button-success"
        (click)="createItem()">
      </button>
    </div>
  `,
  styleUrls: ['./example-audit-trail-with-inactivity.component.css']
})
export class ExampleAuditTrailWithInactivityComponent implements OnInit, OnDestroy {

  constructor(
    public auditTrailService: AuditTrailService,
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService
  ) { }

  ngOnInit() {
    // Initialize any component-specific logic
    this.loggerService.debug('Example component initialized');
  }

  ngOnDestroy() {
    // Clean up inactivity monitor when component is destroyed
    this.auditTrailService.resetInactivityMonitor();
  }

  deleteItem() {
    const itemData = { id: 123, name: 'Example Item' };
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(itemData) },
      { name: AuditTrailFields.RECORD_ID, value: itemData.id.toString() }
    ];

    // Use audit trail service with inactivity monitoring
    this.auditTrailService.auditTrailSelectReason(
      ReasonTypeEnum.CONFIG,
      AuditTrailActions.DEL_USR, // Replace with appropriate action
      ReasonActionTypeEnum.DELETE,
      () => {
        // This callback will be executed after reason selection
        this.performDelete(itemData);
      },
      at_attributes
    );
  }

  updateItem() {
    const oldData = { id: 123, name: 'Old Name' };
    const newData = { id: 123, name: 'New Name' };
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(oldData) },
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(newData) },
      { name: AuditTrailFields.RECORD_ID, value: newData.id.toString() }
    ];

    this.auditTrailService.auditTrailSelectReason(
      ReasonTypeEnum.CONFIG,
      AuditTrailActions.MOD_USR, // Replace with appropriate action
      ReasonActionTypeEnum.UPDATE,
      () => {
        this.performUpdate(newData);
      },
      at_attributes
    );
  }

  createItem() {
    const newData = { name: 'New Item', type: 'Example' };
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(newData) }
    ];

    this.auditTrailService.auditTrailSelectReason(
      ReasonTypeEnum.CONFIG,
      AuditTrailActions.ADD_USR, // Replace with appropriate action
      ReasonActionTypeEnum.CREATE,
      () => {
        this.performCreate(newData);
      },
      at_attributes
    );
  }

  private performDelete(item: any) {
    // Implement your delete logic here
    this.loggerService.debug('Deleting item:', item);
    // After successful deletion, you might want to reset inactivity monitor
    this.auditTrailService.resetInactivityMonitor();
  }

  private performUpdate(item: any) {
    // Implement your update logic here
    this.loggerService.debug('Updating item:', item);
    this.auditTrailService.resetInactivityMonitor();
  }

  private performCreate(item: any) {
    // Implement your create logic here
    this.loggerService.debug('Creating item:', item);
    this.auditTrailService.resetInactivityMonitor();
  }
}
