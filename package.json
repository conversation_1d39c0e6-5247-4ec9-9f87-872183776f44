{"name": "verazial-app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "jest"}, "private": true, "dependencies": {"@angular/animations": "^17.1.0", "@angular/cdk": "^16.1.0", "@angular/common": "^17.1.0", "@angular/compiler": "^17.1.0", "@angular/core": "^17.1.0", "@angular/forms": "^17.1.0", "@angular/material": "^16.1.0", "@angular/platform-browser": "^17.1.0", "@angular/platform-browser-dynamic": "^17.1.0", "@angular/router": "^17.1.0", "@angular/ssr": "^17.1.0", "@material/tab": "^14.0.0", "@ngx-grpc/common": "^3.1.2", "@ngx-grpc/core": "^3.1.2", "@ngx-grpc/grpc-web-client": "^3.1.2", "@ngx-grpc/well-known-types": "^3.1.2", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@types/swiper": "^6.0.0", "angular": "^1.8.3", "chart.js": "^4.4.6", "crypto-js": "^4.2.0", "drawflow": "file:drawflow-0.0.50.tgz", "file-saver": "^2.0.5", "google-protobuf": "^3.21.4", "grpc-web": "^1.5.0", "guid-typescript": "^1.0.9", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "jwt-decode": "^4.0.0", "mammoth": "^1.9.1", "mongodb": "^5.7.0", "ng-multiselect-dropdown": "^1.0.0", "ngx-doc-viewer": "^15.0.1", "ngx-verazial-ui-lib": "file:ngx-verazial-ui-lib-1.0.1.tgz", "primeflex": "^3.3.1", "primeicons": "^7.0.0", "primeng": "^17.14.1", "protoc-gen-grpc-web": "^1.5.0", "rxjs": "~7.8.0", "swiper": "^9.4.1", "ts-protoc-gen": "^0.15.0", "tslib": "^2.3.0", "uuid": "^9.0.1", "xlsx": "^0.18.5", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-builders/custom-webpack": "^17.0.2", "@angular-builders/jest": "^17.0.0", "@angular-devkit/build-angular": "^17.1.0", "@angular/cli": "~17.1.0", "@angular/compiler-cli": "^17.1.0", "@ngx-grpc/protoc-gen-ng": "^3.1.2", "@types/crypto-js": "^4.2.2", "@types/drawflow": "^0.0.11", "@types/file-saver": "^2.0.7", "@types/google-protobuf": "^3.15.12", "@types/jasmine": "~4.3.0", "@types/jest": "^29.5.2", "@types/uuid": "^9.0.8", "browser-sync": "^3.0.0", "jasmine-core": "~4.6.0", "javascript-obfuscator": "^4.1.1", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-preset-angular": "^13.1.1", "typescript": "~5.3.3", "webpack-obfuscator": "^3.5.1"}}