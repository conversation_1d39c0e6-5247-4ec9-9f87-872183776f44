.p-component-overlay {
    background-color: rgba(18, 34, 62, 0.8);
}

.p-component-overlay-enter {
    animation: p-component-overlay-enter-animation none;
}

.p-component-overlay-leave {
    animation: p-component-overlay-leave-animation none;
}

.p-card .p-card-content {
    padding: 0 !important;
}

.p-card .p-card-body {
    padding: .5rem !important;
}

.p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
    border-color: #33AFB3 !important;
    color: #33AFB3 !important;
  }

@media (max-width: 775px) {
    .p-card .p-card-content {
        padding: 0.25rem;
    }
}

@media (max-width: 500px) {
    .p-card .p-card-body {
        padding-left: 0.25rem;
        padding-right: 0.25rem;
    }
}

@media (max-height: 650px){
    .p-card .p-card-body {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem;
    }
}

.p-speeddial-button.p-button.p-button-icon-only {
    width: 3.3rem;
    height: 3.3rem;
    background: #009BA9;
    border-color: #009BA9;

}

.p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action {
    background: #F5F9FF!important;
    color: #424242;
    padding: 1.25rem;
    font-weight: 400;
    font-size: 16px;
    border-style: none!important;
}

.p-panelmenu .p-panelmenu-content {
    border: none!important;
    background: none!important;
    
    font-weight: 400;
    font-size: 16px;
}

.p-panelmenu .p-panelmenu-content .p-menuitem > .p-menuitem-content .p-menuitem-link {
    padding-left: 45px! important;
}

.admin-groups .p-button {
    color: #ffffff;
    background: #64748B;
    border: 1px solid #64748B;
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
    border-radius: 6px;
    outline-color: transparent;
}

.p-picklist .p-picklist-list .p-picklist-item.p-highlight {
    color: #1E9093;
    background: #E1FEFF;
    font-weight: 600;
}

.p-picklist .p-picklist-list .p-picklist-item {
    color: #495057;
}

.p-orderlist .p-orderlist-controls .p-button{
    background: #64748B;
    border: 1px solid #64748B;
}

.p-orderlist .p-orderlist-list .p-orderlist-item.p-highlight {
    color: #1E9093;
    background: #E1FEFF;
    font-weight: 600;
}

.p-orderlist-controls {
    display: none!important;
}

.p-tabview .p-tabview-panels {
    padding: 0 !important;
}

.p-stepper-nav {
    justify-content: center;
}

.p-stepper-header {
    flex: none;
}

.p-stepper-separator {
    width: 100%;
    max-width: 215px;
}

.p-progress-spinner-circle {
    stroke: #33AFB3 !important;
    stroke-linecap: square;
}

@keyframes p-progress-spinner-color {
    100%,
    40% {
        stroke: #036475 !important;
    }
    60% {
        stroke: #33AFB3 !important;
    }
    80%,
    90% {
        stroke: #65D2E4 !important;
    }
}