import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { HttpClientModule, HttpClient } from '@angular/common/http';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';

import { environment } from 'src/environments/environment';

import { GRPC_INTERCEPTORS, GrpcCoreModule } from '@ngx-grpc/core';

import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import {HashLocationStrategy, LocationStrategy} from '@angular/common';
import { DiManagerSettingsModule } from 'src/verazial-common-frontend/core/general/manager/data/di-manager-settings.module';
import { DiKonektorModule } from 'src/verazial-common-frontend/core/general/konektor/data/di-konektor.module';
import { DiSubjectModule } from 'src/verazial-common-frontend/core/general/subject/data/di-subject.module';
import { DiActionsV2Module } from 'src/verazial-common-frontend/core/general/actionsV2/data/di-actionsV2.module';
import { AppLayoutModule } from './layout/app.layout.module';
import { DiAccessModule } from 'src/verazial-common-frontend/core/general/access/data/di-access.module';
import { DiRoleModule } from 'src/verazial-common-frontend/core/general/role/data/di-role.module';
import { DiAuthModule } from 'src/verazial-common-frontend/core/general/auth/data/di-auth.module';
import { DiUserModule } from 'src/verazial-common-frontend/core/general/user/data/di-user.module';
import { DiStorageModule } from 'src/verazial-common-frontend/core/general/storage/data/di-storage.module';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { ToastModule } from 'primeng/toast';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { DiApiBinderModule } from 'src/verazial-common-frontend/core/general/api-binder/data/di-api-binder.module';
import { DiMonitoringModule } from 'src/verazial-common-frontend/core/general/monitoring/data/di-monitoring.module';
import myLocaleEn from '@angular/common/locales/en'
import myLocaleEs from '@angular/common/locales/es'
import myLocalePt from '@angular/common/locales/pt'
import {registerLocaleData} from '@angular/common';
import { DiApplicationModule } from 'src/verazial-common-frontend/core/general/application/data/di-application.module';
import { DiWindowParametersModule } from 'src/verazial-common-frontend/core/general/application/data/di-window-parameters.module';
import { DiApplicationWindowModule } from 'src/verazial-common-frontend/core/general/application/data/di-application-window.module';
import { DiCredentialsModule } from 'src/verazial-common-frontend/core/general/credential/data/di-credentials.module';
import { DiDataSourceModule } from 'src/verazial-common-frontend/core/general/data-source/data/di-data-source.module';
import { DiDataSourceParametersModule } from 'src/verazial-common-frontend/core/general/data-source/data/di-data-source-parameters.module';
import { DiSubjectApplicationModule } from 'src/verazial-common-frontend/core/general/subject-application/data/di-subject-application.module';
import { DiSubjectAppCredentialsModule } from 'src/verazial-common-frontend/core/general/subject-application/data/di-subject-app-credentials.module';

import { EncryptionService } from 'src/verazial-common-frontend/core/services/encryptionService';
import { DiDrawFlowModule } from 'src/verazial-common-frontend/core/general/flow/data/di-draw-flow.module';
import { DiTaskFlowModule } from 'src/verazial-common-frontend/core/general/flow/data/di-task-flow.module';
import { DiGroupCategoryModule } from 'src/verazial-common-frontend/core/general/assignment/categories/data/di-group-category.module';
import { DiAssignmentModule } from 'src/verazial-common-frontend/core/general/assignment/group/data/di-assignment.module';
import { DiTenantModule } from 'src/verazial-common-frontend/core/general/tenant/data/di-tenant.module';
import { DiApplicationFlowModule } from 'src/verazial-common-frontend/core/general/application-flow/data/di-application-flow.module';
import { DiPrisonsModule } from 'src/verazial-common-frontend/core/general/prisons/data/di-prisons.module';
import { DiBiographicModule } from 'src/verazial-common-frontend/core/general/biographic/data/di-biographic.module';
import { DiBiometricModule } from 'src/verazial-common-frontend/core/general/biometric/data/di-biometric.module';
import { DiAppRegistryModule } from 'src/verazial-common-frontend/core/general/app-registry/data/di-app-registry.module';
import { DiLicenseModule } from 'src/verazial-common-frontend/core/general/license/data/di-license.module';
import { InactivityMonitorModule } from 'src/verazial-common-frontend/modules/shared/components/inactivity-monitor/inactivity-monitor.module';

registerLocaleData(myLocaleEn);
registerLocaleData(myLocaleEs);
registerLocaleData(myLocalePt);

// AoT requires an exported function for factories
export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './verazial-common-frontend/assets/i18n/', '.json');
}

export function getDefaultLanguage(): string {
  const encryptionService = new EncryptionService();
  let localStorageService = new LocalStorageService(encryptionService);
  let lang = localStorageService.getLanguage();
  let language: string;
  if (lang) {
    language = lang;
  }
  else {
    language = environment.defaultLanguage;
  }
  return language
}

@NgModule({
  declarations: [
    AppComponent,
  ],
  imports: [
    /* Forms */
    FormsModule,
    ReactiveFormsModule,
    /* PrimeNG Modules */
    DropdownModule,
    ToastModule,
    ConfirmDialogModule,
    /* */
    BrowserModule,
    BrowserAnimationsModule,
    AppRoutingModule,
    HttpClientModule,
    TranslateModule.forRoot({
      defaultLanguage: getDefaultLanguage(),
      loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient]
      }
    }),
    // Custom
    AppLayoutModule,
    InactivityMonitorModule,
    // gRPC
    GrpcCoreModule.forRoot(),
    DiDrawFlowModule,
    DiTaskFlowModule,
    DiGroupCategoryModule,
    DiAssignmentModule,
    // New modules
    DiManagerSettingsModule,
    DiKonektorModule,
    DiSubjectModule,
    DiAuthModule,
    DiActionsV2Module,
    DiTenantModule,
    DiAccessModule,
    DiRoleModule,
    DiUserModule,
    DiStorageModule,
    DiApiBinderModule,
    DiMonitoringModule,
    DiApplicationModule,
    DiWindowParametersModule,
    DiApplicationWindowModule,
    DiCredentialsModule,
    DiDataSourceModule,
    DiDataSourceParametersModule,
    DiSubjectApplicationModule,
    DiSubjectAppCredentialsModule,
    DiApplicationFlowModule,
    DiPrisonsModule,
    DiBiographicModule,
    DiBiometricModule,
    DiAppRegistryModule,
    DiLicenseModule,
  ],
  providers: [
    // httpInterceptorProviders,
    {
      provide: LocationStrategy,
      useClass: HashLocationStrategy
    },
    ConfirmationService,
    MessageService,
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
