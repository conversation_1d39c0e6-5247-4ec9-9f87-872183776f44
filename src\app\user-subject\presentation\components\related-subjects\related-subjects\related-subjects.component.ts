import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, <PERSON>Chang<PERSON> } from "@angular/core";
import { Form<PERSON>uilder, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { TranslateService } from "@ngx-translate/core";
import { ConfirmationService, FilterService, MessageService, TreeNode } from "primeng/api";
import { Table } from "primeng/table";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { RoleEntity } from "src/verazial-common-frontend/core/general/common/entity/role.entity";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { LanguageRecordModel, TranslationGroup, TranslationModel } from "src/verazial-common-frontend/core/general/manager/common/models/translation.model";
import { GetAllRolesUseCase } from "src/verazial-common-frontend/core/general/role/domain/use-cases/roles/get-all-roles.use-case";
import { RelatedSubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/related-subject.entity";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { DeleteRelatedSubjectByIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/delete-related-subject.use-case";
import { GetAllSubjectsUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-all-subjects.use-case";
import { GetRelatedSubjectsByRelatedSubjectIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-related-subjects-by-related-subject-id.use-case";
import { GetRelatedSubjectsBySubjectIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-related-subjects-by-subject-id.use-case";
import { GetSubjectByIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subject-by-id.use-case";
import { SaveRelatedSubjectUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/save-related-subject.use-case";
import { UpdateRelatedSubjectUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/update-related-subject.use-case";
import { AccessIdentifier } from "src/verazial-common-frontend/core/models/access-identifier.enum";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { GenericKeyValue } from "src/verazial-common-frontend/core/models/key-value.interface";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { CheckPermissionsService } from "src/verazial-common-frontend/core/services/check-permissions-service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { EntryExitService } from "src/verazial-common-frontend/core/services/entry-exit.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { NewLocationsService } from "src/verazial-common-frontend/core/services/new-locations.service";
import { ValidatorService } from "src/verazial-common-frontend/modules/shared/services/validator.service";

@Component({
    selector: 'app-related-subjects',
    templateUrl: './related-subjects.component.html',
    styleUrl: './related-subjects.component.css'
})
export class RelatedSubjectsComponent implements OnInit, OnChanges, OnDestroy {

    // Inputs
    @Input() readAndWritePermissions: boolean = false;
    @Input() userSubject: SubjectEntity | undefined;
    @Input() userIsVerified: boolean = false;
    @Input() managerSettings?: GeneralSettings;

    canReadAndWrite: boolean = false;
    readOnly: boolean = false;
    isDisableSaveButton: boolean = true;

    searchValue: string | undefined;
    showRelatedSubjectDialog: boolean = false;
    showRelationsToSubjectDialog: boolean = false;

    listOfRelatedSubjects: RelatedSubjectEntity[] = [];
    selectedRelatedSubjects: RelatedSubjectEntity[] = [];

    listOfRelatedSubjectsToThisSubject: RelatedSubjectEntity[] = [];
    selectedNodes!: TreeNode[];
    data: TreeNode[] = [];
    imagePlaceholder: string = "verazial-common-frontend/assets/images/all/UserPic.svg";

    listOfRoles: RoleEntity[] = [];

    filteredValues: any[] = [];

    access_identifier = AccessIdentifier.RELATED_SUBJECTS;

    // New Related Subject
    public form: FormGroup = this.fb.group({
        subjectId: ['', [Validators.required]],
        relatedSubjectId: ['', [Validators.required]],
        relationship: ['', [Validators.required]],
        isVisitor: [false],
        comments: [''],
    });
    relatedSubject: RelatedSubjectEntity | undefined;
    isNew: boolean = true;
    // Options
    mainSubjectSelected: GenericKeyValue | undefined;
    relatedSubjectSelected: GenericKeyValue | undefined;
    relationshipSelected: GenericKeyValue | undefined;
    lLocationOptions: any[] = [];
    lCentreOptions: any[] = [];

    mainSubjectOptions: GenericKeyValue[] = [];
    relatedSubjectOptions: GenericKeyValue[] = [];
    allSubjects: GenericKeyValue[] = [];
    allSubs: SubjectEntity[] = [];
    relationshipOptions: GenericKeyValue[] = [];
    relationshipsParameter: string = 'relationships';

    isLoading: boolean = false;

    confirmDialogTimeoutLimit: number = 0;
    startCheckingInactivity: boolean = false;

    constructor(
        private router: Router,
        private checkPermissions: CheckPermissionsService,
        private fb: FormBuilder,
        private validatorService: ValidatorService,
        private localStorageService: LocalStorageService,
        private loggerService: ConsoleLoggerService,
        private translateService: TranslateService,
        private auditTrailService: AuditTrailService,
        private messageService: MessageService,
        private confirmationService: ConfirmationService,
        private filterService: FilterService,
        private getAllSubjectsUseCase: GetAllSubjectsUseCase,
        private saveRelatedSubjectUseCase: SaveRelatedSubjectUseCase,
        private getRelatedSubjectsBySubjectIdUseCase: GetRelatedSubjectsBySubjectIdUseCase,
        private getRelatedSubjectsByRelatedSubjectIdUseCase: GetRelatedSubjectsByRelatedSubjectIdUseCase,
        private updateRelatedSubjectUseCase: UpdateRelatedSubjectUseCase,
        private deleteRelatedSubjectByIdUseCase: DeleteRelatedSubjectByIdUseCase,
        private getSubjectByIdUseCase: GetSubjectByIdUseCase,
        private entryExitService: EntryExitService,
        private getAllRolesUseCase: GetAllRolesUseCase,
        private newLocationsService: NewLocationsService,
    ) {
        this.filterService.register('customString', (value: any, filter: any): boolean => {
            if (!filter) return true; // If no filter provided, show all
            else if (typeof value === 'string' && typeof filter === 'string') {
                return this.getSubjectName(value).toLowerCase().includes(filter.toLowerCase());
            }
            return false;
        });
    }

    ngOnInit() {
        this.isLoading = true;
        this.showRelatedSubjectDialog = false;
        this.showRelationsToSubjectDialog = false;
        let options = this.localStorageService.getSessionSettings()?.continued1?.newLocations!;
        this.lLocationOptions = this.newLocationsService.locationsToTreeSelectOptions(options, false, false);
        this.lCentreOptions = this.newLocationsService.centresToTreeSelectOptions(options);
        this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier) && this.readAndWritePermissions;
        if (!this.canReadAndWrite) {
            this.readOnly = this.checkPermissions.hasReadPermissions(this.access_identifier);
        }
        this.mainSubjectOptions = [
            { key: this.userSubject?.id!, value: (this.userSubject?.names + ' ' + this.userSubject?.lastNames).trim() }
        ];
        this.mainSubjectSelected = this.mainSubjectOptions[0];
        this.form.get('subjectId')?.disable();
        let settings = this.localStorageService.getSessionSettings();
        if (settings) {
            this.loggerService.debug(settings.catalogs!);
            let options = settings.catalogs?.find((catalog) => catalog.parameter === this.relationshipsParameter)?.options;
            this.relationshipOptions = options ? (JSON.parse(options) as string[]).map((option) => { return { key: option, value: this.getLabel(option, this.relationshipsParameter) } }) : [{ key: this.translateService.instant('content.other'), value: this.translateService.instant('content.other') }];
        }
        this.getAllSubjectsUseCase.execute({ offset: 0, limit: 10000 }).then(
            (subjects) => {
                this.allSubs = subjects;
                this.allSubjects = this.allSubs
                    .map((subject: SubjectEntity) => {
                        return { key: subject.id!, value: (subject.names + ' ' + subject.lastNames).trim() }
                    });
                let subjOptions = this.allSubs;
                if (settings?.continued1?.prisonsSettings?.entryExitControlRoleRelationRestrictions?.length! > 0) {
                    let restrictions = settings?.continued1?.prisonsSettings?.entryExitControlRoleRelationRestrictions!;
                    let subjectRoleIds = this.userSubject?.roles?.map((role) => role.id?.toString());
                    let relatedRoles = restrictions.filter((restriction) => subjectRoleIds?.includes(restriction.entryRole)).map((restriction) => restriction.relatedRole);
                    let reverseRelatedRoles = restrictions.filter((restriction) => subjectRoleIds?.includes(restriction.relatedRole)).map((restriction) => restriction.entryRole);
                    let allRestrictedRoles = relatedRoles.concat(reverseRelatedRoles);
                    if(allRestrictedRoles.length > 0) subjOptions = this.allSubs.filter((subject) => subject.roles?.find((role) => allRestrictedRoles.includes(role.id?.toString())));
                }
                this.relatedSubjectOptions = subjOptions
                    .filter((subject: SubjectEntity) => subject.id !== this.userSubject?.id)
                    .filter((subject: SubjectEntity) => this.listOfRelatedSubjects.findIndex((relatedSubject: RelatedSubjectEntity) => relatedSubject.relatedSubjectId === subject.id) === -1)
                    .map((subject: SubjectEntity) => {
                        return { key: subject.id!, value: (subject.names + ' ' + subject.lastNames + " - " + this.listOfRoles.find(v => v.id === Number(subject.defaultRole))?.name).trim() }
                    });
            },
            (e) => {
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_SUBJECTS, 0, 'ERROR', '', at_attributes);
            }
        )
        .finally(() => {
            this.getAllRoles(() => {
                this.getSubjectRelationsTree();
            });
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['userSubject'] && changes['userSubject'].currentValue) {
            this.ngOnInit();
        }
        if (changes['readAndWritePermissions'] && changes['readAndWritePermissions'].currentValue) {
            this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier) && this.readAndWritePermissions;
        }
    }

    ngOnDestroy() {
        // Clean up the timeout if the component is destroyed
        this.closeConfirmationDialog();
    }

    resetInactivityMonitor() {
        this.startCheckingInactivity = false;
        this.confirmDialogTimeoutLimit = 0;
    }

    getAllRoles(_callback: Function) {
        this.getAllRolesUseCase.execute().then(
            (data) => {
                this.listOfRoles = data;
            },
            (e) => {
                this.loggerService.error(e);
            }
        )
        .finally(() => {
            _callback();
        });
    }

    getSubjectRelationsTree() {
        this.isLoading = true;
        let mainSubject: TreeNode = {
            expanded: true,
            type: 'person',
            data: {
                numId: '',
                id: this.userSubject?.id,
                image: this.userSubject?.pic,
                name: this.userSubject?.names + ' ' + this.userSubject?.lastNames,
                role: this.listOfRoles.find((role) => role.id === Number(this.userSubject?.defaultRole))?.name ?? '',
                title: ''
            },
            children: []
        };
        this.getRelatedSubjectsBySubjectIdUseCase.execute({ id: this.userSubject?.id! }).then(
            (data) => {
                this.listOfRelatedSubjects = data;
                this.entryExitService.setRelatedSubjects(data);

                for (let rs of data) {
                    this.getSubjectByIdUseCase.execute({ id: rs.relatedSubjectId! }).then(
                        (subject) => {
                            let relatedSubject: TreeNode = {
                                expanded: true,
                                type: 'person',
                                data: {
                                    numId: subject.numId,
                                    id: subject.id,
                                    image: subject.pic,
                                    name: subject.names + ' ' + subject.lastNames,
                                    role: this.listOfRoles.find((role) => role.id === Number(subject.defaultRole))?.name ?? '',
                                    title: rs.relationship
                                },
                                children: []
                            };
                            mainSubject.children?.push(relatedSubject);
                        },
                        (e) => {
                            this.loggerService.error(e);
                            const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SUBJECT_BY_ID, 0, 'ERROR', '', at_attributes);
                        }
                    );
                }
            },
            (e) => {
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_RELATED_SUBJECTS_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
            }
        )
        .finally(() => {
            this.getRelatedSubjectsByRelatedSubjectIdUseCase.execute({ id: this.userSubject?.id! }).then(
                (data) => {
                    this.listOfRelatedSubjectsToThisSubject = data;
                    for (let rs of data) {
                        this.getSubjectByIdUseCase.execute({ id: rs.subjectId! }).then(
                            (subject) => {
                                let relatedSubject: TreeNode = {
                                    expanded: true,
                                    type: 'person',
                                    data: {
                                        numId: subject.numId,
                                        id: subject.id,
                                        image: subject.pic,
                                        name: subject.names + ' ' + subject.lastNames,
                                        role: this.listOfRoles.find((role) => role.id === Number(subject.defaultRole))?.name ?? '',
                                        title: rs.relationship
                                    },
                                    children: []
                                };
                                let rsExists = mainSubject.children?.find((child) => child.data.id === rs.subjectId);
                                if (rsExists === undefined) {
                                    mainSubject.children?.push(relatedSubject);
                                }
                                else {
                                    if (rsExists.data.title !== rs.relationship) {
                                        mainSubject.children!.find((child) => child.data.id === rs.subjectId)!.data.title = rsExists.data.title + ' / ' + rs.relationship;
                                    }
                                }
                            },
                            (e) => {
                                this.loggerService.error(e);
                                const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SUBJECT_BY_ID, 0, 'ERROR', '', at_attributes);
                            }
                        );
                    }
                    this.data = [mainSubject];
                },
                (e) => {
                    this.loggerService.error(e);
                    const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_RELATED_SUBJECTS_BY_RELATED_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
                }
            )
            .finally(() => {
                this.isLoading = false;
            });
        });
    }

    editRelatedSubject(relatedSubject: RelatedSubjectEntity) {
        if(!relatedSubject) {
            if (this.selectedRelatedSubjects.length === 1) {
                relatedSubject = this.selectedRelatedSubjects[0];
            }
            else {
                return;
            }
        }
        this.isNew = false;
        this.relatedSubject = {...relatedSubject};
        this.relatedSubjectOptions = this.allSubjects;
        this.relatedSubjectSelected = this.allSubjects.find((subject) => subject.key === relatedSubject.relatedSubjectId);
        this.form.get('relatedSubjectId')?.setValue(this.relatedSubjectSelected);
        this.relationshipSelected = this.relationshipOptions.find((relationship) => relationship.key === relatedSubject.relationship);
        this.form.get('relationship')?.setValue(this.relationshipSelected);
        this.form.get('isVisitor')?.setValue(relatedSubject.isVisitor);
        this.form.get('comments')?.setValue(relatedSubject.comments);
        this.showRelatedSubjectDialog = true;
        if (this.readOnly || !this.userIsVerified) {
            this.form.disable();
        }
        else {
            this.form.enable();
            this.form.get('subjectId')?.disable();
            this.form.get('relatedSubjectId')?.disable();
        }
    }

    async deleteRelatedSubject(relatedSubject?: RelatedSubjectEntity) {
        let relatedSubjectsToDelete: RelatedSubjectEntity[] = [];
        let reverseRelatedSubjectsToDelete: RelatedSubjectEntity[] = [];
        if (this.selectedRelatedSubjects.length >= 1 && !relatedSubject) {
        relatedSubjectsToDelete = this.selectedRelatedSubjects;
        } else if (relatedSubject) {
        relatedSubjectsToDelete.push(relatedSubject);
        }
        if (relatedSubjectsToDelete.length > 0) {
            for (let relatedSubject of relatedSubjectsToDelete) {
                let relatedSubjectRelationsList = await this.getRelatedSubjectsBySubjectIdUseCase.execute({ id: relatedSubject.relatedSubjectId! });
                reverseRelatedSubjectsToDelete = [...reverseRelatedSubjectsToDelete, ...relatedSubjectRelationsList.filter((rs) => rs.relatedSubjectId === relatedSubject.subjectId)];
            }
        }
        console.log(relatedSubjectsToDelete);
        console.log(reverseRelatedSubjectsToDelete);
        this.confirmDelete(relatedSubjectsToDelete, reverseRelatedSubjectsToDelete);
    }

     // Confirmation Dialogs
    confirmDelete(relatedSubjectsToDelete: RelatedSubjectEntity[], reverseRelatedSubjectsToDelete: RelatedSubjectEntity[]) {
        let message: string = ""
        if (relatedSubjectsToDelete.length == 1) {
            message = `${this.translateService.instant('messages.delete_single_record')} <b>${this.getSubjectName(relatedSubjectsToDelete[0].relatedSubjectId!)}</b>?`;
        } else {
            message = this.translateService.instant('messages.delete_multiple_records') + "<br>(" + relatedSubjectsToDelete.length + ")<br>";
        }
        this.confirmationService.confirm({
            message: message,
            header: this.translateService.instant('messages.delete_confirmation_header'),
            icon: 'pi pi-exclamation-triangle',
            rejectButtonStyleClass: "p-button-text",
            acceptButtonStyleClass: "ng-confirm-button",
            acceptIcon: "none",
            rejectIcon: "none",
            acceptLabel: this.translateService.instant("delete"),
            rejectLabel: this.translateService.instant("no"),
            accept: () => {
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(relatedSubjectsToDelete) },
                ];
                this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.SUBJECT, AuditTrailActions.DELETE_RELATED_SUBJECT, ReasonActionTypeEnum.DELETE, () => { this.onSubmitDelete([...relatedSubjectsToDelete, ...reverseRelatedSubjectsToDelete]); }, at_attributes);
            },
            reject: () => {
                this.resetInactivityMonitor();
            }
        });

        this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
        this.startCheckingInactivity = false;
        setTimeout(() => this.startCheckingInactivity = true, 0);
    }

    async onSubmitDelete(relatedSubjectsToDelete: RelatedSubjectEntity[]) {
        if (relatedSubjectsToDelete.length === 0) return;

        this.isLoading = true;

        try {
            // Run all deletions in parallel
            await Promise.all(relatedSubjectsToDelete.map(subject => this.deleteRelatedSubjectById(subject)));

            // Success message after all deletions
            this.messageService.add({
                severity: 'success',
                summary: this.translateService.instant('content.successTitle'),
                detail: this.translateService.instant('messages.success_general'),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });

            // Clear selected items
            this.selectedRelatedSubjects = [];
        } catch (error: any) {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('content.errorTitle'),
                detail: `${this.translateService.instant('messages.error_deleting_related_subjects')}: ${error.message}`,
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });

            this.loggerService.error('Error deleting related subjects:');
            this.loggerService.error(error);
        } finally {
            this.isLoading = false;
        }
    }

    deleteRelatedSubjectById(relatedSubject: RelatedSubjectEntity): Promise<void> {
        return new Promise(async (resolve, reject) => {
            try {
                const response = await this.deleteRelatedSubjectByIdUseCase.execute({ id: relatedSubject.id! });

                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(relatedSubject) },
                ];

                if (response.success) {
                    // Remove the deleted subject from the list
                    this.listOfRelatedSubjects = [...this.listOfRelatedSubjects.filter(sub => sub.id !== relatedSubject.id)];
                    this.entryExitService.setRelatedSubjects(this.listOfRelatedSubjects);
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DELETE_RELATED_SUBJECT, 0, 'SUCCESS', '', at_attributes);
                } else {
                    at_attributes.push({ name: AuditTrailFields.ERROR, value: JSON.stringify(response) });
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DELETE_RELATED_SUBJECT, 0, 'ERROR', '', at_attributes);
                }

                resolve();
            } catch (error) {
                this.loggerService.error(error);

                const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(error) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DELETE_RELATED_SUBJECT, 0, 'ERROR', '', at_attributes);

                reject(error);
            } finally {
                this.getSubjectRelationsTree();
            }
        });
    }

    onRelatedSubjectSelectionChange(event: any) {
        //this.loggerService.debug(this.selectedUsersSubjects)
    }

    trackDataChange() {
        if (!this.relatedSubject) {
            this.relatedSubject = new RelatedSubjectEntity();
        }
        if (this.isNew) this.relatedSubject.id = undefined;
        this.relatedSubject.subjectId = this.userSubject?.id;
        if (this.form.get('relatedSubjectId')?.touched && this.form.get('relatedSubjectId')?.value) {
            this.relatedSubject.relatedSubjectId = this.form.get('relatedSubjectId')?.value.key;
        }
        if (this.form.get('relationship')?.touched && this.form.get('relationship')?.value) {
            this.relatedSubject.relationship = this.form.get('relationship')?.value.key;
        }
        this.relatedSubject.isVisitor = this.form.get('isVisitor')?.value;
        this.relatedSubject.comments = this.form.get('comments')?.value;
        this.isDisableSaveButton = !this.form.valid;
    }

    saveRelatedSubject(relatedSubject?: RelatedSubjectEntity, isNew: boolean = this.isNew) {
        console.log('saveRelatedSubject');
        console.log(relatedSubject);
        console.log(isNew ? 'isNew' : 'isNotNew');
        let isValid = true;
        if (!relatedSubject){
            relatedSubject = { ...this.relatedSubject };
            isValid = this.form.valid
        }
        if(isValid && relatedSubject) {
            this.isLoading = true;
            if(isNew) {
                this.saveRelatedSubjectUseCase.execute({relatedSubject: relatedSubject}).then(
                    (data) => {
                        this.loggerService.debug(data);
                        this.listOfRelatedSubjects = [...this.listOfRelatedSubjects, data];
                        this.entryExitService.setRelatedSubjects(this.listOfRelatedSubjects);
                        this.onCancelDialog();
                        const at_attributes: ExtraData[] = [
                            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(data) },
                        ];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.SAVE_RELATED_SUBJECT, 0, 'SUCCESS', '', at_attributes);
                        this.saveReverseRelatedSubject(relatedSubject!);
                    },
                    (e) => {
                        this.loggerService.error(e);
                        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.SAVE_RELATED_SUBJECT, 0, 'ERROR', '', at_attributes);
                    }
                )
                .finally(() => {
                    this.getSubjectRelationsTree();
                });
            }
            else {
                this.updateRelatedSubjectUseCase.execute({relatedSubject: relatedSubject}).then(
                    (data) => {
                        this.loggerService.debug(data);
                        const index = this.listOfRelatedSubjects.findIndex((relatedSubject) => relatedSubject.id === data.id);
                        this.listOfRelatedSubjects[index] = data;
                        this.entryExitService.setRelatedSubjects(this.listOfRelatedSubjects);
                        this.onCancelDialog();
                        const at_attributes: ExtraData[] = [
                            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(relatedSubject) },
                            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(data) },
                        ];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.UPDATE_RELATED_SUBJECT, 0, 'SUCCESS', '', at_attributes);
                        this.saveReverseRelatedSubject(relatedSubject!);
                    },
                    (e) => {
                        this.loggerService.debug(e);
                        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.UPDATE_RELATED_SUBJECT, 0, 'ERROR', '', at_attributes);
                    }
                )
                .finally(() => {
                    this.getSubjectRelationsTree();
                });
            }
        }
    }

    saveReverseRelatedSubject(relatedSubject: RelatedSubjectEntity) {
        let exists = false;
        this.getRelatedSubjectsBySubjectIdUseCase.execute({ id: relatedSubject.relatedSubjectId! }).then(
            (rsData) => {
                if (rsData.find((rs) => rs.relatedSubjectId === relatedSubject.subjectId)) {
                    exists = true;
                }
            },
            (e) => {
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_RELATED_SUBJECTS_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
            }
        )
        .finally(() => {
            if (!exists) {
                let reverseRelation :RelatedSubjectEntity = {
                    subjectId: relatedSubject.relatedSubjectId,
                    relatedSubjectId: relatedSubject.subjectId,
                }
                this.saveRelatedSubject(reverseRelation, true);
            }
        });
    }

    onCancelDialog() {
        this.relatedSubject = undefined;
        this.isNew = true;
        this.showRelatedSubjectDialog = false;
        this.form.reset();
        this.mainSubjectSelected = this.mainSubjectOptions[0];
        this.relatedSubjectSelected = undefined;
        this.relationshipSelected = undefined;
        let subjOptions = this.allSubs;
        let settings = this.localStorageService.getSessionSettings();
        if (settings?.continued1?.prisonsSettings?.entryExitControlRoleRelationRestrictions?.length! > 0) {
            let restrictions = settings?.continued1?.prisonsSettings?.entryExitControlRoleRelationRestrictions!;
            let subjectRoleIds = this.userSubject?.roles?.map((role) => role.id?.toString());
            let relatedRoles = restrictions.filter((restriction) => subjectRoleIds?.includes(restriction.entryRole)).map((restriction) => restriction.relatedRole);
            let reverseRelatedRoles = restrictions.filter((restriction) => subjectRoleIds?.includes(restriction.relatedRole)).map((restriction) => restriction.entryRole);
            let allRestrictedRoles = relatedRoles.concat(reverseRelatedRoles);
            if(allRestrictedRoles.length > 0) subjOptions = this.allSubs.filter((subject) => subject.roles?.find((role) => allRestrictedRoles.includes(role.id?.toString())));
        }
        this.relatedSubjectOptions = subjOptions
            .filter((subject: SubjectEntity) => subject.id !== this.userSubject?.id)
            .filter((subject: SubjectEntity) => this.listOfRelatedSubjects.findIndex((relatedSubject: RelatedSubjectEntity) => relatedSubject.relatedSubjectId === subject.id) === -1)
            .map((subject: SubjectEntity) => {
                return { key: subject.id!, value: (subject.names + ' ' + subject.lastNames).trim() }
            });
        this.form.get('subjectId')?.disable();
        this.form.get('relatedSubjectId')?.enable();
    }

    navigateToSubject(numId: string) {
        if (numId) {
            this.router.navigate(['general/subject/edit', 'subject', numId], { state: { verified: false } });
        }
    }

    getSubjectName(subjectId: string): string {
        return this.allSubjects.find((subject) => subject.key === subjectId)?.value.toString() || '';
    }

    getSubjectLocationName(subjectId: string): string {
        let subject = this.allSubs.find((subject) => subject.id === subjectId);
        return this.getLocationName(subject?.center!);
    }

    getLocationName(centerId: string ): string {
        if(centerId != "")
        {
          return this.newLocationsService.getNameAndTree(centerId, this.lLocationOptions) ?? "";
        }
        else
        {
          return "";
        }
      }

    isRequiredField(field: string): boolean {
      return this.validatorService.isRequiredField(this.form, field);
    }

    isValid(field: string): boolean {
      return this.validatorService.isValidField(this.form, field);
    }

    /* Search */
    onFilter(event: any, dt: Table) {
        this.filteredValues = event.filteredValue;
    }

    closeConfirmationDialog() {
        this.confirmationService.close();
        this.resetInactivityMonitor();
    }

    getLabel(key: string, catalogParameter: string): string {
        let reasonTranslations = this.managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'catalogs-' + catalogParameter)?.translations || [];
        let translations: LanguageRecordModel[] = reasonTranslations.find((t: TranslationModel) => t.key === key)?.translations || [];
        let translation = translations.find(t => t.languageCode == this.translateService.currentLang);
        if (translation && translation.value) {
            return translation.value
        }
        return key;
    }
}