# InactivityMonitorComponent Implementation Guide with AuditTrailService

This guide explains how to implement the InactivityMonitorComponent in components that use the AuditTrailService.

## Overview

The InactivityMonitorComponent monitors user activity and automatically closes confirmation dialogs when the user is inactive for a specified period. The AuditTrailService has been enhanced to support inactivity monitoring for audit trail reason selection dialogs.

## Key Features

- **Automatic Dialog Timeout**: Dialogs automatically close after a configured inactivity period
- **Activity Detection**: Monitors mouse movement, clicks, keyboard input, scroll, and touch events
- **Configurable Timeouts**: Uses session settings for timeout configuration
- **Integration with AuditTrailService**: Seamless integration with existing audit trail functionality

## Implementation Steps

### 1. Import Required Modules

In your component module, import the InactivityMonitorModule:

```typescript
import { InactivityMonitorModule } from 'src/verazial-common-frontend/modules/shared/components/inactivity-monitor/inactivity-monitor.module';

@NgModule({
  imports: [
    // ... other imports
    InactivityMonitorModule
  ]
})
```

### 2. Add InactivityMonitorComponent to Template

Add the component to your template (usually near the top):

```html
<!-- Required PrimeNG components -->
<p-toast></p-toast>
<p-confirmDialog [style]="{width: '395px'}"></p-confirmDialog>

<!-- Inactivity Monitor Component -->
<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>
```

### 3. Inject AuditTrailService in Component

```typescript
import { AuditTrailService } from 'src/verazial-common-frontend/core/services/audit-trail.service';

@Component({
  // ...
})
export class YourComponent implements OnDestroy {
  
  constructor(
    public auditTrailService: AuditTrailService,
    // ... other dependencies
  ) { }

  ngOnDestroy() {
    // Clean up inactivity monitor when component is destroyed
    this.auditTrailService.resetInactivityMonitor();
  }
}
```

### 4. Use AuditTrailService Methods

Use the existing audit trail methods as usual. The inactivity monitoring is automatically handled:

```typescript
deleteItem() {
  const at_attributes: ExtraData[] = [
    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(itemData) }
  ];

  this.auditTrailService.auditTrailSelectReason(
    ReasonTypeEnum.CONFIG,
    AuditTrailActions.DEL_USR,
    ReasonActionTypeEnum.DELETE,
    () => {
      // This callback executes after reason selection
      this.performDelete();
    },
    at_attributes
  );
}
```

## AuditTrailService Enhancements

The AuditTrailService now includes the following inactivity monitoring features:

### Properties
- `confirmDialogTimeoutLimit: number` - Timeout limit in milliseconds
- `startCheckingInactivity: boolean` - Flag to start/stop monitoring

### Methods
- `resetInactivityMonitor()` - Resets monitoring state
- `closeConfirmationDialog()` - Closes dialog and resets monitoring
- `getInactivityMonitorConfig()` - Returns current configuration
- `initializeInactivityMonitor()` - Initializes monitoring with session settings

### Configuration

The inactivity timeout is automatically configured from session settings:
- `timeoutMessages` - Used for dialog timeout (default: 10 seconds)
- Timeout is converted to milliseconds: `(timeoutMessages ?? 10) * 1000`

## Component Input Properties

The InactivityMonitorComponent accepts these inputs:

- `intervalStart: number` - Delay before starting monitoring (default: 5000ms)
- `interval: number` - Check interval (default: 1000ms)  
- `inactivityTimeoutLimit: number` - Timeout limit in milliseconds
- `startChecking: boolean` - Flag to start/stop monitoring

## Events Monitored

The component automatically detects these user activities:
- Mouse movement (`mousemove`)
- Mouse clicks (`click`)
- Keyboard input (`keydown`)
- Page scrolling (`scroll`)
- Touch events (`touchstart`)

## Best Practices

1. **Always include ngOnDestroy**: Clean up the inactivity monitor when the component is destroyed
2. **Use public auditTrailService**: Make the service public so the template can access its properties
3. **Reset after operations**: Call `resetInactivityMonitor()` after completing operations
4. **Consistent placement**: Place the InactivityMonitorComponent near the top of your template
5. **Include required PrimeNG components**: Ensure `p-toast` and `p-confirmDialog` are included

## Example Usage

See the provided example files:
- `example-audit-trail-with-inactivity.component.ts`
- `example-audit-trail-with-inactivity.component.html`
- `example-audit-trail-with-inactivity.component.css`
- `example-audit-trail-with-inactivity.module.ts`

## Troubleshooting

1. **Monitor not starting**: Ensure `startCheckingInactivity` is set to `true`
2. **Timeout not working**: Check that `confirmDialogTimeoutLimit` is greater than 0
3. **Module not found**: Verify InactivityMonitorModule is imported in your module
4. **Dialog not closing**: Ensure the `expired` event is bound to `closeConfirmationDialog()`
